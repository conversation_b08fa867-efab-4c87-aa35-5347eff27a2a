{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/InputError.jsx"], "sourcesContent": ["export default function InputError({ message, className = \"\", ...props }) {\r\n  return message ? (\r\n    <p {...props} className={\"error-message\" + className}>\r\n      {message}\r\n    </p>\r\n  ) : null;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO;IACtE,OAAO,wBACL,6LAAC;QAAG,GAAG,KAAK;QAAE,WAAW,kBAAkB;kBACxC;;;;;eAED;AACN;KANwB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/assets/svgIcons/SvgIcon.js"], "sourcesContent": ["export const EyeIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"12\"\r\n      viewBox=\"0 0 18 12\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 9.70508C9.9375 9.70508 10.7345 9.37708 11.391 8.72108C12.047 8.06458 12.375 7.26758 12.375 6.33008C12.375 5.39258 12.047 4.59558 11.391 3.93908C10.7345 3.28308 9.9375 2.95508 9 2.95508C8.0625 2.95508 7.2655 3.28308 6.609 3.93908C5.953 4.59558 5.625 5.39258 5.625 6.33008C5.625 7.26758 5.953 8.06458 6.609 8.72108C7.2655 9.37708 8.0625 9.70508 9 9.70508ZM9 8.35508C8.4375 8.35508 7.9595 8.15808 7.566 7.76408C7.172 7.37058 6.975 6.89258 6.975 6.33008C6.975 5.76758 7.172 5.28933 7.566 4.89533C7.9595 4.50183 8.4375 4.30508 9 4.30508C9.5625 4.30508 10.0408 4.50183 10.4347 4.89533C10.8282 5.28933 11.025 5.76758 11.025 6.33008C11.025 6.89258 10.8282 7.37058 10.4347 7.76408C10.0408 8.15808 9.5625 8.35508 9 8.35508ZM9 11.9551C7.175 11.9551 5.5125 11.4456 4.0125 10.4266C2.5125 9.40808 1.425 8.04258 0.75 6.33008C1.425 4.61758 2.5125 3.25183 4.0125 2.23283C5.5125 1.21433 7.175 0.705078 9 0.705078C10.825 0.705078 12.4875 1.21433 13.9875 2.23283C15.4875 3.25183 16.575 4.61758 17.25 6.33008C16.575 8.04258 15.4875 9.40808 13.9875 10.4266C12.4875 11.4456 10.825 11.9551 9 11.9551ZM9 10.4551C10.4125 10.4551 11.7095 10.0831 12.891 9.33908C14.072 8.59558 14.975 7.59258 15.6 6.33008C14.975 5.06758 14.072 4.06433 12.891 3.32033C11.7095 2.57683 10.4125 2.20508 9 2.20508C7.5875 2.20508 6.2905 2.57683 5.109 3.32033C3.928 4.06433 3.025 5.06758 2.4 6.33008C3.025 7.59258 3.928 8.59558 5.109 9.33908C6.2905 10.0831 7.5875 10.4551 9 10.4551Z\"\r\n        fill=\"#101014\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const CloseEye = () => (\r\n  <svg\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    width=\"22.016\"\r\n    height=\"17.613\"\r\n    viewBox=\"0 0 22.016 17.613\"\r\n  >\r\n    <path\r\n      id=\"Icon_awesome-eye-slash\"\r\n      data-name=\"Icon awesome-eye-slash\"\r\n      d=\"M11.008,13.76A4.935,4.935,0,0,1,6.092,9.181L2.484,6.392A11.465,11.465,0,0,0,1.221,8.3a1.113,1.113,0,0,0,0,1,11.033,11.033,0,0,0,9.787,6.1,10.685,10.685,0,0,0,2.679-.36L11.9,13.67a4.958,4.958,0,0,1-.894.09Zm10.8,2L18,12.819A11.4,11.4,0,0,0,20.8,9.308a1.113,1.113,0,0,0,0-1,11.033,11.033,0,0,0-9.787-6.1A10.6,10.6,0,0,0,5.94,3.5L1.564.116a.55.55,0,0,0-.773.1l-.675.869a.55.55,0,0,0,.1.772L20.452,17.5a.55.55,0,0,0,.773-.1l.676-.869a.55.55,0,0,0-.1-.772Zm-6.32-4.885L14.131,9.829a3.26,3.26,0,0,0-3.994-4.194,1.639,1.639,0,0,1,.32.97,1.6,1.6,0,0,1-.053.344L7.872,4.992a4.9,4.9,0,0,1,3.136-1.139,4.951,4.951,0,0,1,4.954,4.954,4.836,4.836,0,0,1-.478,2.068Z\"\r\n      transform=\"translate(0 0)\"\r\n      fill=\"#fff\"\r\n    />\r\n  </svg>\r\n);\r\nexport const ThemeIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"32\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 32 32\"\r\n    >\r\n      <g\r\n        id=\"Group_175598\"\r\n        data-name=\"Group 175598\"\r\n        transform=\"translate(-1847 -26)\"\r\n      >\r\n        <g\r\n          id=\"Group_175597\"\r\n          data-name=\"Group 175597\"\r\n          transform=\"translate(1842.007 28.533)\"\r\n        >\r\n          <rect\r\n            id=\"Rectangle_13566\"\r\n            data-name=\"Rectangle 13566\"\r\n            width=\"32\"\r\n            height=\"32\"\r\n            rx=\"16\"\r\n            transform=\"translate(4.993 -2.533)\"\r\n            fill=\"#f45126\"\r\n          />\r\n          <g\r\n            id=\"Group_175601\"\r\n            data-name=\"Group 175601\"\r\n            transform=\"translate(6.923 -0.604)\"\r\n          >\r\n            <path\r\n              id=\"Path_113806\"\r\n              data-name=\"Path 113806\"\r\n              d=\"M41.464,28.649a4.427,4.427,0,0,1-.409.578.185.185,0,1,0,.283.237,4.839,4.839,0,0,0,.444-.625.185.185,0,0,0-.317-.189Zm.521-1.312a4.5,4.5,0,0,1-.208.677.185.185,0,0,0,.343.136,4.837,4.837,0,0,0,.225-.733.184.184,0,1,0-.36-.08Zm.086-1.409a4.537,4.537,0,0,1,.012.708.184.184,0,1,0,.368.023,4.831,4.831,0,0,0-.013-.766.185.185,0,1,0-.367.035Zm-.355-1.366a4.469,4.469,0,0,1,.231.669.185.185,0,0,0,.357-.093,4.833,4.833,0,0,0-.251-.724.184.184,0,1,0-.338.148Zm-.764-1.187a4.508,4.508,0,0,1,.43.563.185.185,0,1,0,.31-.2,4.786,4.786,0,0,0-.465-.609.185.185,0,1,0-.275.246Z\"\r\n              transform=\"translate(-23.867 -12.612)\"\r\n              fill=\"#fff\"\r\n              fillRule=\"evenodd\"\r\n            />\r\n            <path\r\n              id=\"Path_113807\"\r\n              data-name=\"Path 113807\"\r\n              d=\"M13.748,7.475a6.273,6.273,0,1,0,6.273,6.273A6.276,6.276,0,0,0,13.748,7.475Zm.369.75a5.535,5.535,0,0,1,0,11.046ZM12.7,4.095v1.7a1.048,1.048,0,0,0,2.1,0v-1.7a1.048,1.048,0,0,0-2.1,0Zm7.133,2.087-1.2,1.2a1.048,1.048,0,0,0,1.482,1.482l1.2-1.2a1.048,1.048,0,1,0-1.482-1.482ZM23.4,12.7h-1.7a1.048,1.048,0,0,0,0,2.1h1.7a1.048,1.048,0,0,0,0-2.1Zm-2.087,7.133-1.2-1.2a1.048,1.048,0,0,0-1.482,1.482l1.2,1.2a1.048,1.048,0,1,0,1.482-1.482ZM14.8,23.4v-1.7a1.048,1.048,0,0,0-2.1,0v1.7a1.048,1.048,0,0,0,2.1,0ZM7.663,21.315l1.2-1.2a1.048,1.048,0,0,0-1.482-1.482l-1.2,1.2a1.048,1.048,0,1,0,1.482,1.482ZM4.095,14.8h1.7a1.048,1.048,0,0,0,0-2.1h-1.7a1.048,1.048,0,0,0,0,2.1ZM6.181,7.663l1.2,1.2A1.048,1.048,0,0,0,8.863,7.381l-1.2-1.2A1.048,1.048,0,0,0,6.181,7.663Z\"\r\n              fill=\"#fff\"\r\n              fillRule=\"evenodd\"\r\n            />\r\n          </g>\r\n        </g>\r\n      </g>\r\n    </svg>\r\n  );\r\n};\r\nexport const GlobalIcons = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-global.svg\" alt=\"Global Icons\" />\r\n  );\r\n};\r\nexport const GlobalBlueIcons = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-blue-global.svg\" alt=\"Global Blue Icons\" />\r\n  );\r\n};\r\nexport const UserBlackIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-black.svg\" alt=\"User Black Icon\" />\r\n  );\r\n};\r\nexport const UserBluekIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-brand-blue.svg\" alt=\"User Blue Icon\" />\r\n  );\r\n};\r\nexport const UserSolidBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-account.svg\" alt=\"User Solid Blue Icon\" />\r\n  );\r\n};\r\nexport const SearchIcons = ({ width = 18, height = 18 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-search.svg\" width={width} height={height} alt=\"Search Icon\" />\r\n  );\r\n};\r\nexport const RoketIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-rocket.svg\" alt=\"Rocket Icon\" />\r\n  );\r\n};\r\nexport const FilterIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-filter.svg\" alt=\"Filter Icon\" />\r\n  );\r\n};\r\nexport const DashboardIcon = ({ color }) => {\r\n  return (\r\n    <img\r\n      src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-dashboard.svg\"\r\n      alt=\"Dashboard Icon\"\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const DynamicIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-dynamic.svg\" alt=\"Dynamic Icon\" />\r\n  );\r\n};\r\nexport const KpiIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kpi.svg\" alt=\"KPI Icon\" />\r\n  );\r\n};\r\nexport const GraphsIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-graphs.svg\" alt=\"Graph Icon\" />\r\n  );\r\n};\r\nexport const ChartIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-charts.svg\" alt=\"Chart Icon\" />\r\n  );\r\n};\r\nexport const TrendIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trend.svg\" alt=\"Trend Icon\" />\r\n  );\r\n};\r\nexport const RealTimeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-real-time.svg\" alt=\"Real Time Icon\" />\r\n  );\r\n};\r\nexport const BrushIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-customize.svg\" alt=\"Brush Icon\" />\r\n  );\r\n};\r\nexport const LearningIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-learning.svg\" alt=\"Learning Icon\" />\r\n  );\r\n};\r\nexport const NextArrowIcon = () => {\r\n  return (\r\n    // <svg\r\n    //   width=\"26\"\r\n    //   height=\"22\"\r\n    //   viewBox=\"0 0 26 22\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M13.9387 21.1081C13.7989 20.9687 13.6879 20.8031 13.6122 20.6208C13.5365 20.4385 13.4975 20.243 13.4975 20.0456C13.4975 19.8481 13.5365 19.6527 13.6122 19.4703C13.6879 19.288 13.7989 19.1224 13.9387 18.9831L20.3749 12.5468L1.99995 12.5468C1.60212 12.5468 1.2206 12.3888 0.93929 12.1075C0.657985 11.8262 0.49995 11.4446 0.49995 11.0468C0.49995 10.649 0.657985 10.2675 0.939291 9.98616C1.2206 9.70485 1.60212 9.54682 1.99995 9.54682L20.3749 9.54682L13.9387 3.10807C13.6569 2.82628 13.4986 2.44409 13.4986 2.04557C13.4986 1.64706 13.6569 1.26486 13.9387 0.98307C14.2205 0.701278 14.6027 0.542968 15.0012 0.542968C15.3997 0.542968 15.7819 0.701278 16.0637 0.98307L25.0637 9.98307C25.2035 10.1224 25.3145 10.288 25.3902 10.4703C25.4659 10.6527 25.5049 10.8481 25.5049 11.0456C25.5049 11.243 25.4659 11.4385 25.3902 11.6208C25.3145 11.8031 25.2035 11.9687 25.0637 12.1081L16.0637 21.1081C15.9243 21.2479 15.7588 21.3589 15.5764 21.4346C15.3941 21.5103 15.1986 21.5493 15.0012 21.5493C14.8038 21.5493 14.6083 21.5103 14.426 21.4346C14.2436 21.3589 14.0781 21.2479 13.9387 21.1081Z\"\r\n    //     fill=\"white\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-next-arrow.svg\" alt=\"Next Arrow Icon\" />\r\n  );\r\n};\r\nexport const PrevIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-prev-arrow.svg\" alt=\"Prev Icon\" />\r\n  );\r\n};\r\nexport const QuoteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-quote.svg\" alt=\"Quote Icon\"\r\n      width=\"31\"\r\n      height=\"26\"\r\n    />\r\n  );\r\n};\r\nexport const CheckIcon = ({ height = 28, width = 28 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success.svg\" alt=\"Check Icon\"\r\n      width={width}\r\n      height={height}\r\n    />\r\n  );\r\n};\r\nexport const RedCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-x.svg\" alt=\"Red Cross Icon\" />\r\n  );\r\n};\r\nexport const PlusIcon = ({ color, height = 32, width = 33 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\" alt=\"Plus Icon\"\r\n      width={width}\r\n      height={height}\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const MinusIcon = ({ width = '29px', height = '4px' }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg\" height={height} width={width} alt=\"Minus Icon\" />\r\n  );\r\n};\r\nexport const RedInfoStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-red-warning-star.svg\" alt=\"Red Info Star Icon\" />\r\n  );\r\n};\r\nexport const GreenCheckStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-green-check-star.svg\" alt=\"Green Check Star\"\r\n      width=\"42\"\r\n      height=\"42\"\r\n    />\r\n  );\r\n};\r\nexport const NoteSettingBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-note-setting.svg\" alt=\"Note Setting Blue Icon\" />\r\n  );\r\n};\r\nexport const SettingIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"38\"\r\n      height=\"40\"\r\n      viewBox=\"0 0 38 40\"\r\n      fill=\"none\">\r\n      <path\r\n        d=\"M19.0264 0.5C20.4944 0.516 21.9564 0.686 23.3904 1.006C23.6955 1.0741 23.9716 1.23581 24.1803 1.46852C24.389 1.70124 24.5198 1.99334 24.5544 2.304L24.8944 5.358C24.9424 5.78857 25.0908 6.20186 25.3277 6.56461C25.5645 6.92737 25.8832 7.22946 26.2581 7.44658C26.633 7.6637 27.0537 7.78979 27.4862 7.8147C27.9187 7.83961 28.351 7.76264 28.7484 7.59L31.5484 6.36C31.8329 6.2347 32.1493 6.20088 32.4538 6.26323C32.7584 6.32557 33.036 6.48099 33.2484 6.708C35.2725 8.87031 36.7803 11.4633 37.6584 14.292C37.7501 14.5895 37.7471 14.9081 37.6496 15.2038C37.5521 15.4994 37.3651 15.7574 37.1144 15.942L34.6324 17.774C34.2827 18.0303 33.9983 18.3655 33.8023 18.7522C33.6063 19.1389 33.5041 19.5664 33.5041 20C33.5041 20.4336 33.6063 20.8611 33.8023 21.2478C33.9983 21.6345 34.2827 21.9697 34.6324 22.226L37.1184 24.056C37.3695 24.2407 37.5568 24.499 37.6543 24.7951C37.7518 25.0912 37.7546 25.4102 37.6624 25.708C36.7849 28.5366 35.2778 31.1295 33.2544 33.292C33.0424 33.5189 32.7652 33.6745 32.4611 33.7372C32.1569 33.7999 31.8408 33.7666 31.5564 33.642L28.7444 32.408C28.3476 32.234 27.9154 32.1559 27.4827 32.18C27.0501 32.204 26.6292 32.3296 26.2542 32.5466C25.8791 32.7635 25.5604 33.0657 25.3238 33.4287C25.0872 33.7917 24.9394 34.2053 24.8924 34.636L24.5524 37.688C24.5184 37.995 24.3905 38.2841 24.1861 38.5157C23.9817 38.7473 23.7108 38.9101 23.4104 38.982C20.5136 39.6726 17.4951 39.6726 14.5984 38.982C14.2976 38.9105 14.0262 38.7478 13.8214 38.5162C13.6167 38.2845 13.4885 37.9953 13.4544 37.688L13.1164 34.64C13.0673 34.2106 12.9182 33.7987 12.6811 33.4374C12.4439 33.0761 12.1254 32.7754 11.751 32.5595C11.3766 32.3437 10.9568 32.2186 10.5253 32.1943C10.0938 32.1701 9.6626 32.2474 9.26638 32.42L6.45438 33.652C6.1697 33.7771 5.85318 33.8106 5.54862 33.7479C5.24406 33.6852 4.96652 33.5293 4.75438 33.302C2.73064 31.137 1.22419 28.5412 0.348384 25.71C0.256177 25.4122 0.259017 25.0932 0.35651 24.7971C0.454002 24.501 0.641304 24.2427 0.892384 24.058L3.37838 22.226C3.72808 21.9697 4.01246 21.6345 4.20848 21.2478C4.40451 20.8611 4.50666 20.4336 4.50666 20C4.50666 19.5664 4.40451 19.1389 4.20848 18.7522C4.01246 18.3655 3.72808 18.0303 3.37838 17.774L0.892384 15.946C0.641304 15.7613 0.454002 15.503 0.35651 15.2069C0.259017 14.9108 0.256177 14.5918 0.348384 14.294C1.22647 11.4653 2.73423 8.87231 4.75838 6.71C4.97075 6.48299 5.24841 6.32757 5.55296 6.26523C5.8575 6.20288 6.17389 6.2367 6.45838 6.362L9.25838 7.592C9.65642 7.76454 10.0894 7.84132 10.5225 7.81617C10.9556 7.79102 11.3767 7.66465 11.7521 7.44719C12.1275 7.22974 12.4467 6.92727 12.684 6.56408C12.9212 6.2009 13.07 5.78712 13.1184 5.356L13.4584 2.304C13.4927 1.99271 13.6236 1.69997 13.8327 1.46683C14.0418 1.23369 14.3186 1.07185 14.6244 1.004C16.0564 0.686667 17.5237 0.518667 19.0264 0.5ZM19.0024 14C17.4111 14 15.885 14.6321 14.7597 15.7574C13.6345 16.8826 13.0024 18.4087 13.0024 20C13.0024 21.5913 13.6345 23.1174 14.7597 24.2426C15.885 25.3679 17.4111 26 19.0024 26C20.5937 26 22.1198 25.3679 23.245 24.2426C24.3702 23.1174 25.0024 21.5913 25.0024 20C25.0024 18.4087 24.3702 16.8826 23.245 15.7574C22.1198 14.6321 20.5937 14 19.0024 14Z\"\r\n        fill=\"#FEA500\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const SolidSettingIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-yellow-wrench.svg\" alt=\"Solid Setting Icon\" />\r\n  );\r\n};\r\nexport const RightSolidArrowIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-solid-arrow.svg\" alt=\"\" />\r\n  );\r\n};\r\nexport const SolidInfoIcon = ({ width = '20px', height = '20px' }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg\" height={height} width={width} alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\nexport const WhiteInfoIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-white.svg\" alt=\"White Info Marker Icon\" />\r\n  );\r\n};\r\nexport const RightArrowIcon = ({ color }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg\" alt=\"Right Arrow Icon\"\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const CartIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"25\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 25 20\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M20.8333 16C19.3533 16 18.1667 16.89 18.1667 18C18.1667 18.5304 18.4476 19.0391 18.9477 19.4142C19.4478 19.7893 20.1261 20 20.8333 20C21.5406 20 22.2189 19.7893 22.719 19.4142C23.219 19.0391 23.5 18.5304 23.5 18C23.5 17.4696 23.219 16.9609 22.719 16.5858C22.2189 16.2107 21.5406 16 20.8333 16ZM-0.5 0V2H2.16667L6.96667 9.59L5.15333 12.04C4.95333 12.32 4.83333 12.65 4.83333 13C4.83333 13.5304 5.11428 14.0391 5.61438 14.4142C6.11448 14.7893 6.79276 15 7.5 15H23.5V13H8.06C7.97159 13 7.88681 12.9737 7.8243 12.9268C7.76178 12.8799 7.72667 12.8163 7.72667 12.75C7.72667 12.7 7.74 12.66 7.76667 12.63L8.96667 11H18.9C19.9 11 20.78 10.58 21.2333 9.97L26.0067 3.5C26.1 3.34 26.1667 3.17 26.1667 3C26.1667 2.73478 26.0262 2.48043 25.7761 2.29289C25.5261 2.10536 25.187 2 24.8333 2H5.11333L3.86 0M7.5 16C6.02 16 4.83333 16.89 4.83333 18C4.83333 18.5304 5.11428 19.0391 5.61438 19.4142C6.11448 19.7893 6.79276 20 7.5 20C8.20724 20 8.88552 19.7893 9.38562 19.4142C9.88571 19.0391 10.1667 18.5304 10.1667 18C10.1667 17.4696 9.88571 16.9609 9.38562 16.5858C8.88552 16.2107 8.20724 16 7.5 16Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const SignoutIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-signout.svg\" alt=\"Signout Icon\" />\r\n  );\r\n};\r\nexport const HelpIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradreply-helpicon.svg\" alt=\"Help Icon\" />\r\n  );\r\n};\r\nexport const BaseEyeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye.svg\" alt=\"Base Eye Icon\" />\r\n  );\r\n};\r\nexport const UserBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-user-blue.svg\" alt=\"User Blue Icon\" />\r\n  );\r\n};\r\nexport const DollerIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-renew-dollar.svg\" alt=\"Dollar Icon\" />\r\n  );\r\n};\r\nexport const SecurityIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-security.svg\" alt=\"Security Icon\" />\r\n  );\r\n};\r\nexport const LockIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-lock.svg\" alt=\"Lock Icon\" />\r\n  );\r\n};\r\nexport const LinkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-links.svg\" alt=\"Link Icon\" />\r\n  );\r\n};\r\nexport const PaymentIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-payment.svg\" alt=\"Payment Icon\" />\r\n  );\r\n};\r\nexport const PaymentIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"20\"\r\n      height=\"17\"\r\n      viewBox=\"0 0 20 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M18 0.5H2C0.89 0.5 0.00999999 1.39 0.00999999 2.5L0 14.5C0 15.61 0.89 16.5 2 16.5H18C19.11 16.5 20 15.61 20 14.5V2.5C20 1.39 19.11 0.5 18 0.5ZM18 14.5H2V8.5H18V14.5ZM18 4.5H2V2.5H18V4.5Z\"\r\n        fill=\"#00ADEF\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const CartSideIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-carticon.svg\" alt=\"Cart Icon\" />\r\n  );\r\n};\r\nexport const EditIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-editcon.svg\" alt=\"Edit Icon\" />\r\n  );\r\n};\r\n\r\nexport const CrossIcon = ({ color }) => {\r\n  return (\r\n    // <svg\r\n    //   width=\"18\"\r\n    //   height=\"19\"\r\n    //   viewBox=\"0 0 18 19\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z\"\r\n    //     fill=\"white\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-x.svg\" alt=\"White Cirle Cross Icon\"\r\n      width=\"18\"\r\n      height=\"19\"\r\n\r\n      className={color}\r\n    />\r\n  );\r\n};\r\nexport const PublicProfileIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-light.svg\" alt=\"Public Profile Icon\" />\r\n  );\r\n};\r\nexport const SellerDashboardIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge.svg\" alt=\"Seller Dashboard Icon\" />\r\n  );\r\n};\r\nexport const MarketplaceDisputeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gavel.svg\" alt=\"Marketplace Dispute Icon\" />\r\n  );\r\n};\r\nexport const MarketplaceListIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-marketplace-icon.svg\" alt=\"Marketplace List Icon\" />\r\n  );\r\n};\r\nexport const PurchasedProductIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-package.svg\" alt=\"Purchased Product Icon\" />\r\n  );\r\n};\r\nexport const SoldProductIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coin.svg\" alt=\"Sold Product Icon\" />\r\n  );\r\n};\r\nexport const LogoutIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M2 18C1.45 18 0.979333 17.8043 0.588 17.413C0.196667 17.0217 0.000666667 16.5507 0 16V2C0 1.45 0.196 0.979333 0.588 0.588C0.98 0.196667 1.45067 0.000666667 2 0H9V2H2V16H9V18H2ZM13 14L11.625 12.55L14.175 10H6V8H14.175L11.625 5.45L13 4L18 9L13 14Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const CheckGradientIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"211\"\r\n      height=\"208\"\r\n      viewBox=\"0 0 211 208\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M203.341 74.5632C206.051 83.8216 207.504 93.6158 207.504 103.752C207.504 161.052 161.052 207.504 103.752 207.504C46.4519 207.504 0 161.052 0 103.752C0 46.4518 46.4519 -9.15527e-05 103.752 -9.15527e-05C131.892 -9.15527e-05 157.416 11.2021 176.105 29.3936L194.876 22.6914L101.952 115.617L63.9188 77.5842L39.9479 101.556L101.952 163.559L210.127 55.3835L203.341 74.5632Z\"\r\n        fill=\"url(#paint0_radial_490_1337)\"\r\n      />\r\n      <defs>\r\n        <radialGradient\r\n          id=\"paint0_radial_490_1337\"\r\n          cx=\"0\"\r\n          cy=\"0\"\r\n          r=\"1\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n          gradientTransform=\"translate(59.8666 54.0097) rotate(45) scale(197.976)\"\r\n        >\r\n          <stop stopColor=\"#73D1E1\" />\r\n          <stop offset=\"1\" stopColor=\"#395BB2\" />\r\n        </radialGradient>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\nexport const CalculatorIcon = () => {\r\n  return (\r\n    // <svg\r\n    //   width=\"38\"\r\n    //   height=\"38\"\r\n    //   viewBox=\"0 0 38 38\"\r\n    //   fill=\"none\"\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M10.6667 31.5H13.7917V27.3333H17.9583V24.2083H13.7917V20.0417H10.6667V24.2083H6.5V27.3333H10.6667V31.5ZM21.0833 29.9375H31.5V26.8125H21.0833V29.9375ZM21.0833 24.7292H31.5V21.6042H21.0833V24.7292ZM23.375 16.8125L26.2917 13.8958L29.2083 16.8125L31.3958 14.625L28.4792 11.6042L31.3958 8.6875L29.2083 6.5L26.2917 9.41667L23.375 6.5L21.1875 8.6875L24.1042 11.6042L21.1875 14.625L23.375 16.8125ZM7.02084 13.1667H17.4375V10.0417H7.02084V13.1667ZM4.41667 37.75C3.27084 37.75 2.28959 37.3424 1.47292 36.5271C0.656253 35.7118 0.248615 34.7306 0.250004 33.5833V4.41667C0.250004 3.27083 0.658337 2.29028 1.475 1.475C2.29167 0.659722 3.27223 0.251389 4.41667 0.25H33.5833C34.7292 0.25 35.7104 0.658333 36.5271 1.475C37.3438 2.29167 37.7514 3.27222 37.75 4.41667V33.5833C37.75 34.7292 37.3424 35.7104 36.5271 36.5271C35.7118 37.3438 34.7306 37.7514 33.5833 37.75H4.41667Z\"\r\n    //     fill=\"#00ADEF\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-calculator.svg\" alt=\"\" />\r\n  );\r\n};\r\nexport const SolidRedArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"31\"\r\n      height=\"36\"\r\n      viewBox=\"0 0 31 36\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M28.5 13.6699C31.8333 15.5944 31.8333 20.4056 28.5 22.3301L7.5 34.4545C4.16666 36.379 -1.74729e-06 33.9734 -1.57905e-06 30.1243L-5.19101e-07 5.87564C-3.50856e-07 2.02664 4.16667 -0.378984 7.5 1.54552L28.5 13.6699Z\"\r\n        fill=\"#FF696A\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropArrowIcon = ({ width = 24, height = 13 }) => {\r\n  return (\r\n    <svg\r\n      width={width}\r\n      height={height}\r\n      viewBox=\"0 0 24 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropArrowUpIcon = ({ width = 24, height = 13 }) => {\r\n  return (\r\n    <svg\r\n      width={width}\r\n      height={height}\r\n      viewBox=\"0 0 24 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      style={{ transform: 'rotate(180deg)' }}\r\n    >\r\n      <path\r\n        d=\"M3.47599 3.12261e-08L0.857422 2.47368L12.0003 13L23.1431 2.47368L20.5246 2.34528e-07L12.0003 8.03509L3.47599 3.12261e-08Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const DropDownArrowIcon = ({ width = 14, height = 7 }) => {\r\n  return (\r\n    <svg width={width}\r\n      height={height}\r\n      viewBox=\"0 0 14 7\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M0.333008 0.333984L6.99967 7.00065L13.6663 0.333984H0.333008Z\" fill=\"white\" />\r\n    </svg>\r\n\r\n  );\r\n};\r\nexport const BlackDropDownArrowIcon = ({ width = 14, height = 7 }) => {\r\n  return (\r\n    <svg width={width}\r\n      height={height}\r\n      viewBox=\"0 0 14 7\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M0.333252 0.333008L6.99992 6.99967L13.6666 0.333008H0.333252Z\" fill=\"#666666\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const DeleteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-delete.svg\" alt=\"Delete Icon\"\r\n      width=\"20\"\r\n      height=\"21\"\r\n    />\r\n  );\r\n};\r\nexport const TradeIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"48\"\r\n      height=\"48\"\r\n      viewBox=\"0 0 48 48\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M0.75 42.0833H47.25V47.25H0.75V42.0833ZM8.5 34.3333C11.3417 34.3333 13.6667 32.0083 13.6667 29.1667C13.6667 27.875 13.15 26.5833 12.375 25.8083L15.7333 18.8333H16.25C17.5417 18.8333 18.8333 18.3167 19.6083 17.5417L26.5833 21.1583V21.4167C26.5833 24.2583 28.9083 26.5833 31.75 26.5833C34.5917 26.5833 36.9167 24.2583 36.9167 21.4167C36.9167 20.125 36.4 19.0917 35.625 18.0583L38.9833 11.0833H39.5C42.3417 11.0833 44.6667 8.75833 44.6667 5.91667C44.6667 3.075 42.3417 0.75 39.5 0.75C36.6583 0.75 34.3333 3.075 34.3333 5.91667C34.3333 7.20833 34.85 8.5 35.625 9.275L32.2667 16.25H31.75C30.4583 16.25 29.1667 16.7667 28.3917 17.5417L21.4167 14.1833V13.6667C21.4167 10.825 19.0917 8.5 16.25 8.5C13.4083 8.5 11.0833 10.825 11.0833 13.6667C11.0833 14.9583 11.6 16.25 12.375 17.025L9.01667 24H8.5C5.65833 24 3.33333 26.325 3.33333 29.1667C3.33333 32.0083 5.65833 34.3333 8.5 34.3333Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ArtArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"30\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 30 32\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M1.64673 22.9062L27.6275 7.90625\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      <path\r\n        d=\"M1.64673 15.0469L8.64673 8.04688\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      <path\r\n        d=\"M3.14673 29.0469H13.0462\"\r\n        stroke=\"#00ADEF\"\r\n        strokeWidth=\"3\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const RedInfoIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg\" alt=\"Red Info Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const GreyCheckIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-success-gray.svg\" alt=\"Grey Success Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const GreyCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure.svg\" alt=\"Grey Cross Icon\"\r\n      width=\"28\"\r\n      height=\"28\"\r\n    />\r\n  );\r\n};\r\nexport const ReferIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-refer-a-friend.svg\" alt=\"Refer Icon\" />\r\n  );\r\n};\r\nexport const PartnershipIcon = () => {\r\n  return (\r\n    <svg\r\n      id=\"Layer_1\"\r\n      data-name=\"Layer 1\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"20\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 24 24\"\r\n    >\r\n      <path\r\n        d=\"m17.063,2.185c-1.245.06-2.442.603-3.367,1.528l-3.63,3.63c.57-.573,2.687-.179,3.2.334l2.197-2.197c.487-.487,1.096-.785,1.719-.812.424-.021,1.024.069,1.552.597.493.493.597,1.066.597,1.457,0,.654-.299,1.304-.812,1.815l-3.821,3.845c-.961.961-2.424,1.039-3.272.191-.484-.484-1.281-.487-1.767,0s-.487,1.281,0,1.767c.872.872,2.018,1.313,3.2,1.313,1.278,0,2.582-.522,3.582-1.528l3.845-3.821c.976-.973,1.528-2.275,1.528-3.582,0-1.215-.46-2.37-1.313-3.224-.913-.913-2.14-1.373-3.439-1.313Zm-5.922,6.161c-1.278,0-2.603.525-3.606,1.528l-3.821,3.821c-.976.973-1.528,2.275-1.528,3.582,0,1.215.46,2.37,1.313,3.224.913.913,2.14,1.373,3.439,1.313,1.245-.06,2.442-.603,3.367-1.528l3.63-3.63c-.573.573-2.687.179-3.2-.334l-2.197,2.197c-.487.487-1.096.782-1.719.812-.424.021-1.024-.069-1.552-.597-.493-.493-.597-1.069-.597-1.457,0-.654.299-1.304.812-1.815l3.821-3.845c.961-.961,2.424-1.036,3.272-.191.487.487,1.284.487,1.767,0,.487-.487.487-1.281,0-1.767-.872-.872-2.021-1.313-3.2-1.313Z\"\r\n        fill=\"#fff\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ResendCodeIcon = ({ isRotating }) => {\r\n  return (\r\n    <svg\r\n      className={isRotating ? \"rotate\" : \"\"}\r\n      width=\"19\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 19 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M18.7693 1.84688V6.34688C18.7693 6.54579 18.6903 6.73656 18.5497 6.87721C18.409 7.01786 18.2182 7.09688 18.0193 7.09688H13.5193C13.3717 7.09664 13.2275 7.05285 13.1047 6.97101C12.9818 6.88917 12.8859 6.77291 12.8289 6.63679C12.7718 6.50066 12.7562 6.35074 12.784 6.20578C12.8117 6.06082 12.8816 5.92728 12.985 5.82188L14.71 4.09688L14.3068 3.69375C13.2576 2.64569 11.9212 1.93222 10.4666 1.6435C9.01196 1.35479 7.50438 1.5038 6.13442 2.07171C4.76446 2.63961 3.59362 3.60091 2.76988 4.83409C1.94613 6.06728 1.50648 7.517 1.50648 9C1.50648 10.483 1.94613 11.9327 2.76988 13.1659C3.59362 14.3991 4.76446 15.3604 6.13442 15.9283C7.50438 16.4962 9.01196 16.6452 10.4666 16.3565C11.9212 16.0678 13.2576 15.3543 14.3068 14.3063C14.3758 14.2357 14.4582 14.1796 14.5492 14.1413C14.6401 14.103 14.7378 14.0833 14.8365 14.0833C14.9352 14.0833 15.0329 14.103 15.1239 14.1413C15.2148 14.1796 15.2972 14.2357 15.3662 14.3063C15.5065 14.4469 15.5852 14.6373 15.5852 14.8359C15.5852 15.0345 15.5065 15.225 15.3662 15.3656C14.1073 16.6238 12.5037 17.4805 10.758 17.8274C9.01229 18.1743 7.20293 17.9958 5.55867 17.3145C3.91441 16.6331 2.50908 15.4796 1.52035 13.9996C0.531628 12.5197 0.00390625 10.7798 0.00390625 9C0.00390625 7.22017 0.531628 5.4803 1.52035 4.00036C2.50908 2.52042 3.91441 1.36687 5.55867 0.685539C7.20293 0.00421169 9.01229 -0.174293 10.758 0.172592C12.5037 0.519478 14.1073 1.37618 15.3662 2.63438L15.7693 3.0375L17.485 1.32188C17.589 1.21629 17.722 1.14391 17.8672 1.11388C18.0124 1.08384 18.1632 1.0975 18.3006 1.15313C18.4374 1.21109 18.5545 1.30747 18.6377 1.43059C18.7209 1.55371 18.7666 1.69831 18.7693 1.84688Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const ContactCustomerSupport = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 0.875C7.39303 0.875 5.82214 1.35152 4.486 2.24431C3.14985 3.1371 2.10844 4.40605 1.49348 5.8907C0.87852 7.37535 0.717618 9.00901 1.03112 10.5851C1.34463 12.1612 2.11846 13.6089 3.25476 14.7452C4.39106 15.8815 5.8388 16.6554 7.4149 16.9689C8.99099 17.2824 10.6247 17.1215 12.1093 16.5065C13.594 15.8916 14.8629 14.8502 15.7557 13.514C16.6485 12.1779 17.125 10.607 17.125 9C17.1209 6.84638 16.2635 4.78216 14.7407 3.25932C13.2178 1.73648 11.1536 0.87913 9 0.875ZM9 14C8.81458 14 8.63333 13.945 8.47916 13.842C8.32499 13.739 8.20482 13.5926 8.13387 13.4213C8.06291 13.25 8.04434 13.0615 8.08052 12.8796C8.11669 12.6977 8.20598 12.5307 8.33709 12.3996C8.4682 12.2685 8.63525 12.1792 8.81711 12.143C8.99896 12.1068 9.18746 12.1254 9.35877 12.1964C9.53007 12.2673 9.67649 12.3875 9.77951 12.5417C9.88252 12.6958 9.9375 12.8771 9.9375 13.0625C9.9375 13.3111 9.83873 13.5496 9.66292 13.7254C9.4871 13.9012 9.24864 14 9 14ZM9.625 10.1797V10.25C9.625 10.4158 9.55916 10.5747 9.44195 10.6919C9.32474 10.8092 9.16576 10.875 9 10.875C8.83424 10.875 8.67527 10.8092 8.55806 10.6919C8.44085 10.5747 8.375 10.4158 8.375 10.25V9.625C8.375 9.45924 8.44085 9.30027 8.55806 9.18306C8.67527 9.06585 8.83424 9 9 9C9.30904 9 9.61113 8.90836 9.86808 8.73667C10.125 8.56498 10.3253 8.32095 10.4436 8.03544C10.5618 7.74993 10.5928 7.43577 10.5325 7.13267C10.4722 6.82958 10.3234 6.55117 10.1049 6.33265C9.88634 6.11413 9.60793 5.96531 9.30483 5.90502C9.00174 5.84473 8.68757 5.87568 8.40206 5.99394C8.11655 6.1122 7.87252 6.31247 7.70083 6.56942C7.52914 6.82637 7.4375 7.12847 7.4375 7.4375C7.4375 7.60326 7.37166 7.76223 7.25445 7.87944C7.13724 7.99665 6.97826 8.0625 6.8125 8.0625C6.64674 8.0625 6.48777 7.99665 6.37056 7.87944C6.25335 7.76223 6.1875 7.60326 6.1875 7.4375C6.18751 6.90805 6.33695 6.38936 6.61865 5.94107C6.90036 5.49279 7.30287 5.13312 7.77991 4.90344C8.25694 4.67376 8.78912 4.58339 9.31523 4.64273C9.84134 4.70207 10.34 4.90871 10.7539 5.23888C11.1678 5.56905 11.4801 6.00934 11.6549 6.50911C11.8296 7.00888 11.8598 7.54784 11.7418 8.06398C11.6239 8.58013 11.3627 9.05251 10.9882 9.42678C10.6137 9.80106 10.1412 10.062 9.625 10.1797Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n\r\n\r\n  );\r\n};\r\nexport const RedErrorCircle = () => {\r\n  return (\r\n    // <svg\r\n    //   xmlns=\"http://www.w3.org/2000/svg\"\r\n    //   width=\"24\"\r\n    //   height=\"24\"\r\n    //   viewBox=\"0 0 24 24\"\r\n    //   fill=\"#ff696a\"\r\n    // >\r\n    //   <path\r\n    //     d=\"M11.953 2C6.465 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.493 2 11.953 2zM13 17h-2v-2h2v2zm0-4h-2V7h2v6z\"\r\n    //     fill=\"#fffff\"\r\n    //   />\r\n    // </svg>\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-warning.svg\"\r\n      alt=\"Red Error Icon\"\r\n      width=\"24\"\r\n      height=\"24\"\r\n    />\r\n  );\r\n};\r\nexport const BlackErrorCircle = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"17\"\r\n      viewBox=\"0 0 18 17\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M9 0.375C9 0.375 10.6526 0.375 12.1628 1.01376C12.1628 1.01376 13.621 1.63053 14.7452 2.75476C14.7452 2.75476 15.8695 3.87899 16.4862 5.33719C16.4862 5.33719 17.125 6.84739 17.125 8.5C17.125 8.5 17.125 10.1526 16.4862 11.6628C16.4862 11.6628 15.8695 13.121 14.7452 14.2452C14.7452 14.2452 13.621 15.3695 12.1628 15.9862C12.1628 15.9862 10.6526 16.625 9 16.625C9 16.625 7.34739 16.625 5.83719 15.9862C5.83719 15.9862 4.37899 15.3695 3.25476 14.2452C3.25476 14.2452 2.13053 13.121 1.51376 11.6628C1.51376 11.6628 0.875 10.1526 0.875 8.5C0.875 8.5 0.875 6.84739 1.51376 5.33719C1.51376 5.33719 2.13053 3.87899 3.25476 2.75476C3.25476 2.75476 4.37899 1.63053 5.83719 1.01376C5.83719 1.01376 7.34739 0.375 9 0.375ZM9 1.625C9 1.625 7.60087 1.625 6.32413 2.16501C6.32413 2.16501 5.09047 2.68681 4.13864 3.63864C4.13864 3.63864 3.18681 4.59047 2.66502 5.82413C2.66502 5.82413 2.125 7.10087 2.125 8.5C2.125 8.5 2.125 9.89913 2.66502 11.1759C2.66502 11.1759 3.18681 12.4095 4.13864 13.3614C4.13864 13.3614 5.09047 14.3132 6.32413 14.835C6.32413 14.835 7.60087 15.375 9 15.375C9 15.375 10.3991 15.375 11.6759 14.835C11.6759 14.835 12.9095 14.3132 13.8614 13.3614C13.8614 13.3614 14.8132 12.4095 15.335 11.1759C15.335 11.1759 15.875 9.89912 15.875 8.5C15.875 8.5 15.875 7.10087 15.335 5.82413C15.335 5.82413 14.8132 4.59047 13.8614 3.63864C13.8614 3.63864 12.9095 2.68681 11.6759 2.16501C11.6759 2.16501 10.3991 1.625 9 1.625Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n      <path\r\n        d=\"M9 12.875H9.625C9.97018 12.875 10.25 12.5952 10.25 12.25C10.25 11.9048 9.97018 11.625 9.625 11.625V7.875C9.625 7.52982 9.34518 7.25 9 7.25H8.375C8.02982 7.25 7.75 7.52982 7.75 7.875C7.75 8.22018 8.02982 8.5 8.375 8.5V12.25C8.375 12.5952 8.65482 12.875 9 12.875Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n      <path\r\n        d=\"M9.78125 5.0625C9.78125 5.58027 9.36148 6 8.84375 6C8.32602 6 7.90625 5.58027 7.90625 5.0625C7.90625 4.54473 8.32602 4.125 8.84375 4.125C9.36148 4.125 9.78125 4.54473 9.78125 5.0625Z\"\r\n        fill=\"#1C1C1C\"\r\n      />\r\n    </svg>\r\n\r\n  );\r\n};\r\nexport const DeviceMobileSpeaker = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-device-00ADEF.svg\" alt=\"Device Mobile Speaker Icon\"\r\n      width=\"22\"\r\n      height=\"22\"\r\n    />\r\n\r\n  );\r\n};\r\nexport const ViewCartBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-00ADEF.svg\" alt=\"View Cart BaseBlue\" />\r\n  );\r\n};\r\nexport const ViewCartDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-04498C.svg\" alt=\"View Cart Dark Blue\" />\r\n\r\n  );\r\n};\r\nexport const ViewCartGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-cart-808080.svg\" alt=\"View Cart Gray\" />\r\n\r\n  );\r\n};\r\nexport const CheckoutCardBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-00ADEF.svg\" alt=\"Checkout Card BaseBlue\" />\r\n  );\r\n};\r\nexport const CheckoutCardDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-04498C.svg\" alt=\"Checkout Card BaseBlue\" />\r\n  );\r\n};\r\nexport const CheckoutCardGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-card-808080.svg\" alt=\"Checkout Card Gray\" />\r\n  );\r\n};\r\nexport const AccessBaseBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-00ADEF.svg\" alt=\"Access Base Blue\" />\r\n  );\r\n};\r\nexport const AccessDarkBlue = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-04498C.svg\" alt=\"Access Dark Blue\" />\r\n  );\r\n};\r\nexport const AccessGray = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-access-808080.svg\" alt=\"Access Gray\" />\r\n  );\r\n};\r\nexport const TopRightArrowIcon = () => {\r\n  return (\r\n    <svg\r\n      width=\"13\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 13 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M0.832189 10.79L9.12219 2.5H3.54219C3.27697 2.5 3.02262 2.39464 2.83508 2.20711C2.64754 2.01957 2.54219 1.76522 2.54219 1.5C2.54219 1.23478 2.64754 0.98043 2.83508 0.792893C3.02262 0.605357 3.27697 0.5 3.54219 0.5H11.4922C11.7574 0.5 12.0118 0.605357 12.1993 0.792893C12.3868 0.98043 12.4922 1.23478 12.4922 1.5V9.5C12.4922 9.76522 12.3868 10.0196 12.1993 10.2071C12.0118 10.3946 11.7574 10.5 11.4922 10.5H11.5422C11.277 10.5 11.0226 10.3946 10.8351 10.2071C10.6475 10.0196 10.5422 9.76522 10.5422 9.5V3.95L2.28219 12.21C2.18922 12.3037 2.07862 12.3781 1.95677 12.4289C1.83491 12.4797 1.7042 12.5058 1.57219 12.5058C1.44018 12.5058 1.30947 12.4797 1.18761 12.4289C1.06575 12.3781 0.955151 12.3037 0.862187 12.21C0.766468 12.119 0.689713 12.01 0.636353 11.8892C0.582994 11.7684 0.554085 11.6383 0.551295 11.5063C0.548506 11.3742 0.571892 11.243 0.620103 11.12C0.668314 10.9971 0.740396 10.8849 0.832189 10.79Z\"\r\n        fill=\"#00ADEF\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\nexport const MoneyWithWings = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-money-wings.svg\" alt=\"Money Wings Icon\" />\r\n  );\r\n};\r\nexport const FlatBlueBook = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-book-00A6ED.svg\" alt=\"Flat Blue Book\" />\r\n  );\r\n};\r\n\r\nexport const RedCircleCrossIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure-red.svg\" alt=\"Red Cross\" />\r\n  );\r\n};\r\n\r\nexport const WhiteCrossCircle = () => {\r\n  return (\r\n    <svg width=\"28\" height=\"28\" viewBox=\"0 0 28 28\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M14 0C6.2 0 0 6.2 0 14C0 21.8 6.2 28 14 28C21.8 28 28 21.8 28 14C28 6.2 21.8 0 14 0ZM19.4 21L14 15.6L8.6 21L7 19.4L12.4 14L7 8.6L8.6 7L14 12.4L19.4 7L21 8.6L15.6 14L21 19.4L19.4 21Z\" fill=\"white\" />\r\n    </svg>\r\n\r\n  )\r\n}\r\nexport const BlackCross = ({ width = 14, height = 14 }) => {\r\n  return (\r\n    <svg width={width} height={height} viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M1.4 14L0 12.6L5.6 7L0 1.4L1.4 0L7 5.6L12.6 0L14 1.4L8.4 7L14 12.6L12.6 14L7 8.4L1.4 14Z\" fill=\"black\" />\r\n    </svg>\r\n\r\n  )\r\n}\r\n\r\nexport const AddPlusIcon = ({ width = 10, height = 10 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg\" width={width} height={height} alt=\"Add Model Icon\" />\r\n\r\n  );\r\n};\r\nexport const DragDropIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drag-drop-icon.svg\" alt=\"Drap Drop Icon\" />\r\n\r\n  );\r\n};\r\nexport const SolidIncon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-info-marker.svg\" alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\nexport const BlackDownIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-black-down-arrow.svg\" alt=\"Info Marker Icon\" />\r\n  );\r\n};\r\n\r\nexport const YellowInfoHexa = () => {\r\n  return (\r\n    <svg\r\n      width=\"12\"\r\n      height=\"14\"\r\n      viewBox=\"0 0 12 14\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M3.22867 1.53727C4.58133 0.736604 5.25733 0.335938 6 0.335938C6.74267 0.335938 7.41867 0.735938 8.77133 1.53727L9.22867 1.80794C10.5813 2.60927 11.2573 3.00994 11.6287 3.66927C12 4.32927 12 5.12927 12 6.73194V7.27327C12 8.87527 12 9.6766 11.6287 10.3359C11.2573 10.9959 10.5813 11.3959 9.22867 12.1966L8.77133 12.4679C7.41867 13.2686 6.74267 13.6693 6 13.6693C5.25733 13.6693 4.58133 13.2693 3.22867 12.4679L2.77133 12.1966C1.41867 11.3966 0.742667 10.9953 0.371333 10.3359C-3.97364e-08 9.67594 0 8.87594 0 7.27327V6.73194C0 5.12927 -3.97364e-08 4.3286 0.371333 3.66927C0.742667 3.00927 1.41867 2.60927 2.77133 1.80794L3.22867 1.53727ZM6.66667 9.66927C6.66667 9.84608 6.59643 10.0157 6.4714 10.1407C6.34638 10.2657 6.17681 10.3359 6 10.3359C5.82319 10.3359 5.65362 10.2657 5.5286 10.1407C5.40357 10.0157 5.33333 9.84608 5.33333 9.66927C5.33333 9.49246 5.40357 9.32289 5.5286 9.19787C5.65362 9.07284 5.82319 9.0026 6 9.0026C6.17681 9.0026 6.34638 9.07284 6.4714 9.19787C6.59643 9.32289 6.66667 9.49246 6.66667 9.66927ZM6 3.16927C6.13261 3.16927 6.25979 3.22195 6.35355 3.31572C6.44732 3.40949 6.5 3.53666 6.5 3.66927V7.66927C6.5 7.80188 6.44732 7.92906 6.35355 8.02282C6.25979 8.11659 6.13261 8.16927 6 8.16927C5.86739 8.16927 5.74022 8.11659 5.64645 8.02282C5.55268 7.92906 5.5 7.80188 5.5 7.66927V3.66927C5.5 3.53666 5.55268 3.40949 5.64645 3.31572C5.74022 3.22195 5.86739 3.16927 6 3.16927Z\" fill=\"#FEA500\" />\r\n    </svg>\r\n\r\n  );\r\n};\r\n\r\nexport const TripleDotsMenu = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-kebab-menu.svg\" alt=\"Kabab Menu\" />\r\n  );\r\n};\r\n\r\nexport const WhiteDownArrow = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"13\" height=\"6\" viewBox=\"0 0 14 8\" fill=\"none\">\r\n      <path d=\"M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z\" fill=\"white\" />\r\n    </svg>\r\n    // <img src=\"http://cdn.tradereply.com/dev/site-assets/icons/tradereply-white-down-arrow.svg\" width={width} height={height} alt=\"white down arrow\" />\r\n  );\r\n};\r\nexport const BlackDownArrow = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"13\" height=\"6\" viewBox=\"0 0 14 8\" fill=\"none\">\r\n      <path d=\"M0.666504 0.667969L7.33317 7.33463L13.9998 0.667969H0.666504Z\" fill=\"black\" />\r\n    </svg>\r\n  );\r\n};\r\nexport const WhiteCrossIcon = () => {\r\n  return (\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"15\" viewBox=\"0 0 16 15\" fill=\"none\">\r\n      <path\r\n        d=\"M9.05969 7.49973L15.2847 1.2841C15.4043 1.13838 15.4654 0.953383 15.4562 0.765094C15.4469 0.576804 15.368 0.398686 15.2347 0.265385C15.1014 0.132084 14.9232 0.0531312 14.7349 0.0438836C14.5467 0.0346361 14.3617 0.095755 14.2159 0.215352L8.00031 6.44035L1.78469 0.215352C1.63897 0.095755 1.45397 0.0346361 1.26568 0.0438836C1.07739 0.0531312 0.899272 0.132084 0.76597 0.265385C0.632669 0.398686 0.553717 0.576804 0.544469 0.765094C0.535221 0.953383 0.59634 1.13838 0.715938 1.2841L6.94094 7.49973L0.715938 13.7154C0.575101 13.8575 0.496094 14.0496 0.496094 14.2497C0.496094 14.4499 0.575101 14.6419 0.715938 14.7841C0.859293 14.9227 1.0509 15.0002 1.25031 15.0002C1.44972 15.0002 1.64133 14.9227 1.78469 14.7841L8.00031 8.5591L14.2159 14.7841C14.3593 14.9227 14.5509 15.0002 14.7503 15.0002C14.9497 15.0002 15.1413 14.9227 15.2847 14.7841C15.4255 14.6419 15.5045 14.4499 15.5045 14.2497C15.5045 14.0496 15.4255 13.8575 15.2847 13.7154L9.05969 7.49973Z\"\r\n        fill=\"white\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport const ProfileUserDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-dark.svg\" alt=\"Profile Dark Icon\" />\r\n  );\r\n};\r\nexport const BlueLocationIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-location.svg\" alt=\"Blue Location Icon\" />\r\n  );\r\n};\r\nexport const RatingStarIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-star-single.svg\" alt=\"Rating Star Icon\" />\r\n  );\r\n};\r\nexport const ThumbUpIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-up-green.svg\" alt=\"Thumb Up Icon\" />\r\n  );\r\n};\r\nexport const ThumbDownIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-thumb-down-red.svg\" alt=\"Thumb Down Icon\" />\r\n  );\r\n};\r\nexport const BlackShareIcon = ({ width = 18, height = 13 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-icon.svg\" width={width} height={height} alt=\"Black Share Icon\" />\r\n  );\r\n};\r\nexport const StaticListingImg = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png\" alt=\"Black Share Icon\" />\r\n  );\r\n};\r\n\r\nexport const WhiteDropDownArrow = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg\" alt=\"White DropDown Icon\" />\r\n  );\r\n};\r\nexport const WhiteSingleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-single-stack.svg\" alt=\"White Single Stack Icon\" />\r\n  );\r\n};\r\nexport const WhiteDoubleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-double-stack.svg\" alt=\"White Double Stack Icon\" />\r\n  );\r\n};\r\nexport const WhiteTripleStack = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-triple-stack.svg\" alt=\"White Triple Stack Icon\" />\r\n  );\r\n};\r\nexport const OpenNewtabIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-open-new-tab.svg\" alt=\"New Tab Icon\" />\r\n  );\r\n};\r\nexport const RenameIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-edit-pencil.svg\" alt=\"Rename Icon\" />\r\n  );\r\n};\r\nexport const DeleteDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trashcan-dark.svg\" alt=\"Delete Dark Icon\" />\r\n  );\r\n};\r\nexport const EyeDarkInsightIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark-insights.svg\" alt=\"Eye Dark Insight Icon\" />\r\n  );\r\n};\r\nexport const EyeDarkIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-dark.svg\" alt=\"Eye Dark Icon\" />\r\n  );\r\n};\r\nexport const FollowersIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-followers-icon.svg\" alt=\"Followers Icon\" />\r\n  );\r\n};\r\nexport const ShareLightStrIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-share-white-fill.svg\" alt=\"Followers Icon\" />\r\n  );\r\n};\r\nexport const RelistIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-relist-icon.svg\" alt=\"Relist Icon\" />\r\n  );\r\n};\r\nexport const DigitaLAssetIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-digital-asset.svg\" alt=\"Digital Asset Icon\" />\r\n  );\r\n};\r\nexport const LicenseIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-license.svg\" alt=\"license Icon\" />\r\n  );\r\n};\r\nexport const LightEyeIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-eye-light.svg\" alt=\"Light Eye Icon\" />\r\n  );\r\n};\r\nexport const AddBlueIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-add.svg\" alt=\"Add Blue Icon\" />\r\n  );\r\n};\r\nexport const PinIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-pin.svg\" alt=\"Pin Icon\" />\r\n  );\r\n};\r\n\r\nexport const RightArrowIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"8\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 8 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M7.15694 7.21102L1.49994 12.868L0.0859375 11.454L5.03594 6.50401L0.0859375 1.55401L1.49994 0.140015L7.15694 5.79701C7.34441 5.98454 7.44972 6.23885 7.44972 6.50401C7.44972 6.76918 7.34441 7.02349 7.15694 7.21102Z\"\r\n        fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const EditIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"18\"\r\n      viewBox=\"0 0 18 18\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M17.71 4.04C18.1 3.65 18.1 3 17.71 2.63L15.37 0.289999C15 -0.100001 14.35 -0.100001 13.96 0.289999L12.12 2.12L15.87 5.87M0 14.25V18H3.75L14.81 6.93L11.06 3.18L0 14.25Z\" fill=\"#00ADEF\"></path>\r\n    </svg>\r\n  );\r\n};\r\nexport const PlusIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"33\"\r\n      height=\"32\"\r\n      viewBox=\"0 0 33 32\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M32.5 20H20.5V32H12.5V20H0.5V12H12.5V0H20.5V12H32.5V20Z\" fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const RemoveIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"18\"\r\n      height=\"19\"\r\n      viewBox=\"0 0 18 19\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M9 0.75C4.125 0.75 0.25 4.625 0.25 9.5C0.25 14.375 4.125 18.25 9 18.25C13.875 18.25 17.75 14.375 17.75 9.5C17.75 4.625 13.875 0.75 9 0.75ZM12.375 13.875L9 10.5L5.625 13.875L4.625 12.875L8 9.5L4.625 6.125L5.625 5.125L9 8.5L12.375 5.125L13.375 6.125L10 9.5L13.375 12.875L12.375 13.875Z\"\r\n        fill=\"white\">\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const RightSolidArrowIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"12\"\r\n      height=\"13\"\r\n      viewBox=\"0 0 12 13\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M10.9903 5.63999L1.94025 0.769994C1.18025 0.359994 0.300251 1.06999 0.540251 1.89999L1.78025 6.23999C1.83025 6.41999 1.83025 6.59999 1.78025 6.77999L0.540251 11.12C0.300251 11.95 1.18025 12.66 1.94025 12.25L10.9903 7.37999C11.1446 7.29563 11.2735 7.17127 11.3632 7.01995C11.453 6.86862 11.5004 6.69593 11.5004 6.51999C11.5004 6.34406 11.453 6.17136 11.3632 6.02004C11.2735 5.86872 11.1446 5.74435 10.9903 5.65999V5.63999Z\"\r\n        fill=\"#00ADEF\"\r\n      >\r\n      </path>\r\n    </svg>\r\n  );\r\n};\r\nexport const LocationIconSvg = () => {\r\n  return (\r\n    <svg\r\n      width=\"20\"\r\n      height=\"20\"\r\n      viewBox=\"0 0 20 20\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M10.0004 11.1912C11.4363 11.1912 12.6004 10.0272 12.6004 8.59121C12.6004 7.15527 11.4363 5.99121 10.0004 5.99121C8.56445 5.99121 7.40039 7.15527 7.40039 8.59121C7.40039 10.0272 8.56445 11.1912 10.0004 11.1912Z\"\r\n        stroke=\"#292D32\"\r\n        stroke-width=\"1.5\"\r\n      />\r\n      <path\r\n        d=\"M3.01675 7.07533C4.65842 -0.141339 15.3501 -0.133006 16.9834 7.08366C17.9417 11.317 15.3084 14.9003 13.0001 17.117C11.3251 18.7337 8.67508 18.7337 6.99175 17.117C4.69175 14.9003 2.05842 11.3087 3.01675 7.07533Z\"\r\n        stroke=\"#292D32\"\r\n        strokeWidth=\"1.5\"\r\n      />\r\n    </svg>\r\n\r\n  );\r\n};\r\n\r\nexport const BulletPointIcon = ({ width = 8, height = 8 }) => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-bullet-point.svg\" width={width} height={height} alt=\"Bullet Point Icon\" />\r\n  );\r\n};\r\nexport const CoinWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-coins-white.svg\" alt=\"White Coin Icon\" />\r\n  );\r\n};\r\nexport const ProductFormatWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-product-format-white.svg\" alt=\"Product Format White Icon\" />\r\n  );\r\n};\r\nexport const GuageWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-gauge-white.svg\" alt=\"Guage White Icon\" />\r\n  );\r\n};\r\nexport const TradingPlatformWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trading-platform-white.svg\" alt=\"Trading Platform White Icon\" />\r\n  );\r\n};\r\nexport const ShuffleWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-shuffle-white.svg\" alt=\"Shuffle White Icon\" />\r\n  );\r\n};\r\nexport const ClockWhiteIcon = () => {\r\n  return (\r\n    <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-clock-white.svg\" alt=\"Clock White Icon\" />\r\n  );\r\n};\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAM,UAAU;IACrB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KAfa;AAgBN,MAAM,WAAW,kBACtB,6LAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,6LAAC;YACC,IAAG;YACH,aAAU;YACV,GAAE;YACF,WAAU;YACV,MAAK;;;;;;;;;;;MAZE;AAgBN,MAAM,YAAY;IACvB,qBACE,6LAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,6LAAC;YACC,IAAG;YACH,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC;gBACC,IAAG;gBACH,aAAU;gBACV,WAAU;;kCAEV,6LAAC;wBACC,IAAG;wBACH,aAAU;wBACV,OAAM;wBACN,QAAO;wBACP,IAAG;wBACH,WAAU;wBACV,MAAK;;;;;;kCAEP,6LAAC;wBACC,IAAG;wBACH,aAAU;wBACV,WAAU;;0CAEV,6LAAC;gCACC,IAAG;gCACH,aAAU;gCACV,GAAE;gCACF,WAAU;gCACV,MAAK;gCACL,UAAS;;;;;;0CAEX,6LAAC;gCACC,IAAG;gCACH,aAAU;gCACV,GAAE;gCACF,MAAK;gCACL,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;MApDa;AAqDN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;MAJa;AAKN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;MAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;MAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAAkF,KAAI;;;;;;AAEnG;MAJa;AAKN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;MAJa;AAKN,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACrD,qBACE,6LAAC;QAAI,KAAI;QAAyE,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAExH;MAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;MAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;OAJa;AAKN,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE;IACrC,qBACE,6LAAC;QACC,KAAI;QACJ,KAAI;QACJ,WAAW;;;;;;AAGjB;OARa;AASN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,UAAU;IACrB,qBACE,6LAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;OAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAyE,KAAI;;;;;;AAE1F;OAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;OAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;OAJa;AAKN,MAAM,gBAAgB;IAC3B,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,4jCAA4jC;IAC5jC,mBAAmB;IACnB,OAAO;IACP,SAAS;kBACT,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAhBa;AAiBN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAJa;AAKN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;QACnF,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,YAAY,CAAC,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE;IACnD,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAO;QACP,QAAQ;;;;;;AAGd;OAPa;AAQN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE;IACzD,qBACE,6LAAC;QAAI,KAAI;QAAuE,KAAI;QAClF,OAAO;QACP,QAAQ;QACR,WAAW;;;;;;AAGjB;OARa;AASN,MAAM,YAAY,CAAC,EAAE,QAAQ,MAAM,EAAE,SAAS,KAAK,EAAE;IAC1D,qBACE,6LAAC;QAAI,KAAI;QAAwE,QAAQ;QAAQ,OAAO;QAAO,KAAI;;;;;;AAEvH;OAJa;AAKN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;OAJa;AAKN,MAAM,qBAAqB;IAChC,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;QAC9F,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;OAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;kBACL,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAda;AAeN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;OAJa;AAKN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;OAJa;AAKN,MAAM,gBAAgB,CAAC,EAAE,QAAQ,MAAM,EAAE,SAAS,MAAM,EAAE;IAC/D,qBACE,6LAAC;QAAI,KAAI;QAA8E,QAAQ;QAAQ,OAAO;QAAO,KAAI;;;;;;AAE7H;OAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAJa;AAKN,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE;IACtC,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;QACzF,WAAW;;;;;;AAGjB;OANa;AAON,MAAM,WAAW;IACtB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;OAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;OAJa;AAKN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAAuE,KAAI;;;;;;AAExF;OAJa;AAKN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;OAJa;AAKN,MAAM,WAAW;IACtB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAMN,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE;IACjC,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,sSAAsS;IACtS,mBAAmB;IACnB,OAAO;IACP,SAAS;kBACT,6LAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;QAEP,WAAW;;;;;;AAGjB;OArBa;AAsBN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QAAI,KAAI;QAAqF,KAAI;;;;;;AAEtG;OAJa;AAKN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,yBAAyB;IACpC,qBACE,6LAAC;QAAI,KAAI;QAAwE,KAAI;;;;;;AAEzF;OAJa;AAKN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;OAJa;AAKN,MAAM,uBAAuB;IAClC,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;OAJa;AAKN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QAAI,KAAI;QAAuE,KAAI;;;;;;AAExF;OAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;0BACC,cAAA,6LAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,eAAc;oBACd,mBAAkB;;sCAElB,6LAAC;4BAAK,WAAU;;;;;;sCAChB,6LAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKrC;OA5Ba;AA6BN,MAAM,iBAAiB;IAC5B,OACE,OAAO;IACP,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,uCAAuC;IACvC,IAAI;IACJ,UAAU;IACV,o2BAAo2B;IACp2B,qBAAqB;IACrB,OAAO;IACP,SAAS;kBACT,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAhBa;AAiBN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACvD,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACzD,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,OAAO;YAAE,WAAW;QAAiB;kBAErC,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAhBa;AAiBN,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;IAC1D,qBACE,6LAAC;QAAI,OAAO;QACV,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBACN,cAAA,6LAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAInF;OAXa;AAYN,MAAM,yBAAyB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;IAC/D,qBACE,6LAAC;QAAI,OAAO;QACV,QAAQ;QACR,SAAQ;QAAW,MAAK;QAAO,OAAM;kBACrC,cAAA,6LAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAGnF;OARa;AAUN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAAyE,KAAI;QACpF,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,YAAY;IACvB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;0BAEhB,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;0BAEhB,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;gBACZ,eAAc;;;;;;;;;;;;AAItB;OA7Ba;AA8BN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;QAC1F,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;QACrF,OAAM;QACN,QAAO;;;;;;AAGb;OAPa;AAQN,MAAM,YAAY;IACvB,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;OAJa;AAKN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QACC,IAAG;QACH,aAAU;QACV,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAhBa;AAiBN,MAAM,iBAAiB,CAAC,EAAE,UAAU,EAAE;IAC3C,qBACE,6LAAC;QACC,WAAW,aAAa,WAAW;QACnC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAhBa;AAiBN,MAAM,yBAAyB;IACpC,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAMb;OAjBa;AAkBN,MAAM,iBAAiB;IAC5B,OACE,OAAO;IACP,uCAAuC;IACvC,eAAe;IACf,gBAAgB;IAChB,wBAAwB;IACxB,mBAAmB;IACnB,IAAI;IACJ,UAAU;IACV,yHAAyH;IACzH,oBAAoB;IACpB,OAAO;IACP,SAAS;kBACT,6LAAC;QAAI,KAAI;QACP,KAAI;QACJ,OAAM;QACN,QAAO;;;;;;AAGb;OApBa;AAqBN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAKb;OA1Ba;AA2BN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;QAC3F,OAAM;QACN,QAAO;;;;;;AAIb;OARa;AASN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAG/F;OALa;AAMN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAG/F;OALa;AAMN,MAAM,uBAAuB;IAClC,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,uBAAuB;IAClC,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;OAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;OAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;OAJa;AAKN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;OAfa;AAgBN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAMN,MAAM,qBAAqB;IAChC,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAMN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,OAAM;kBAChE,cAAA,6LAAC;YAAK,GAAE;YAAwL,MAAK;;;;;;;;;;;AAI3M;OAPa;AAQN,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACpD,qBACE,6LAAC;QAAI,OAAO;QAAO,QAAQ;QAAQ,SAAQ;QAAY,MAAK;QAAO,OAAM;kBACvE,cAAA,6LAAC;YAAK,GAAE;YAA2F,MAAK;;;;;;;;;;;AAI9G;OAPa;AASN,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACrD,qBACE,6LAAC;QAAI,KAAI;QAAuE,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAGtH;OALa;AAMN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAGlG;OALa;AAMN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;OAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;OAJa;AAMN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YAAK,UAAS;YAAU,UAAS;YAAU,GAAE;YAAm3C,MAAK;;;;;;;;;;;AAI56C;OAba;AAeN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;OAJa;AAMN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAI,SAAQ;QAAW,MAAK;kBACpF,cAAA,6LAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAInF;OAPa;AAQN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAI,SAAQ;QAAW,MAAK;kBACpF,cAAA,6LAAC;YAAK,GAAE;YAAgE,MAAK;;;;;;;;;;;AAGnF;OANa;AAON,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAGb;QARa;AAUN,MAAM,sBAAsB;IACjC,qBACE,6LAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;QAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;QAJa;AAKN,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE;IACxD,qBACE,6LAAC;QAAI,KAAI;QAA6E,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAE5H;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;QAJa;AAMN,MAAM,qBAAqB;IAChC,qBACE,6LAAC;QAAI,KAAI;QAA6E,KAAI;;;;;;AAE9F;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA+E,KAAI;;;;;;AAEhG;QAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;QAJa;AAKN,MAAM,qBAAqB;IAChC,qBACE,6LAAC;QAAI,KAAI;QAAoF,KAAI;;;;;;AAErG;QAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA2E,KAAI;;;;;;AAE5F;QAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAAiF,KAAI;;;;;;AAElG;QAJa;AAKN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QAAI,KAAI;QAAmF,KAAI;;;;;;AAEpG;QAJa;AAKN,MAAM,aAAa;IACxB,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;QAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAA0E,KAAI;;;;;;AAE3F;QAJa;AAKN,MAAM,eAAe;IAC1B,qBACE,6LAAC;QAAI,KAAI;QAA4E,KAAI;;;;;;AAE7F;QAJa;AAKN,MAAM,cAAc;IACzB,qBACE,6LAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;QAJa;AAKN,MAAM,UAAU;IACrB,qBACE,6LAAC;QAAI,KAAI;QAAsE,KAAI;;;;;;AAEvF;QAJa;AAMN,MAAM,oBAAoB;IAC/B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YAAK,UAAS;YACb,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;QAhBa;AAiBN,MAAM,cAAc;IACzB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YAA0K,MAAK;;;;;;;;;;;AAGzL;QAba;AAcN,MAAM,cAAc;IACzB,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YAA0D,MAAK;;;;;;;;;;;AAIzE;QAda;AAeN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;QAfa;AAgBN,MAAM,yBAAyB;IACpC,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAKb;QAhBa;AAiBN,MAAM,kBAAkB;IAC7B,qBACE,6LAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,gBAAa;;;;;;0BAEf,6LAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;;;;;;;AAKpB;QAtBa;AAwBN,MAAM,kBAAkB,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE;IACvD,qBACE,6LAAC;QAAI,KAAI;QAA+E,OAAO;QAAO,QAAQ;QAAQ,KAAI;;;;;;AAE9H;QAJa;AAKN,MAAM,gBAAgB;IAC3B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,yBAAyB;IACpC,qBACE,6LAAC;QAAI,KAAI;QAAuF,KAAI;;;;;;AAExG;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa;AAKN,MAAM,2BAA2B;IACtC,qBACE,6LAAC;QAAI,KAAI;QAAyF,KAAI;;;;;;AAE1G;QAJa;AAKN,MAAM,mBAAmB;IAC9B,qBACE,6LAAC;QAAI,KAAI;QAAgF,KAAI;;;;;;AAEjG;QAJa;AAKN,MAAM,iBAAiB;IAC5B,qBACE,6LAAC;QAAI,KAAI;QAA8E,KAAI;;;;;;AAE/F;QAJa", "debugId": null}}, {"offset": {"line": 2435, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2441, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/TextInput.jsx"], "sourcesContent": ["'use client';\r\nimport { useState } from \"react\";\r\nimport { Form } from \"react-bootstrap\";\r\nimport { CloseEye, EyeIcon } from \"@/assets/svgIcons/SvgIcon\";\r\nimport \"../../css/common/textInput.scss\";\r\n\r\nconst TextInput = (props) => {\r\n  const [active, setActive] = useState(false);\r\n\r\n  /** RESTRICT USER TO ENTER e, E, +, -, . IN INPUT TYPE NUMBER */\r\n  const disabledCharacters = [\"e\", \"E\", \"+\", \"-\"];\r\n  const onKeyDown = (e) => {\r\n    if (props.disableDecimal) {\r\n      disabledCharacters.push(\".\");\r\n    }\r\n\r\n    /** RESTRICT USER TO ENTER MORE THAN MAX LENGTH IN INPUT TYPE NUMBER */\r\n    return props.type === \"number\"\r\n      ? (disabledCharacters.includes(e.key) ||\r\n        (e.key !== \"Backspace\" &&\r\n          props.maxLength &&\r\n          e.target.value.length === props.maxLength)) &&\r\n      e.preventDefault()\r\n      : props.onlyChar;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Form.Group\r\n        className={`customInput ${props.className}`}\r\n        controlId={props.controlId}\r\n      >\r\n        {props.label ? (\r\n          <Form.Label htmlFor={props.id} className={props.classLabel}>\r\n            {props.label}\r\n            <sup>*</sup>\r\n          </Form.Label>\r\n        ) : (\r\n          \"\"\r\n        )}\r\n        <div className=\"customInput_inner\">\r\n          {props.usernameInput && (\r\n            <span className=\"spanInputCounter\">{props.value.length}/20</span>)\r\n          }\r\n          <Form.Control\r\n\r\n            disabled={props.disabled}\r\n            type={props.type === \"password\" && active ? \"text\" : props.type}\r\n            id={props.id}\r\n            name={props.name}\r\n            value={props.value}\r\n            // onKeyDown={onKeyDown}\r\n            placeholder={props.placeholder}\r\n            onBlur={props.onBlur}\r\n            onChange={props.onChange}\r\n            maxLength={props.maxLength ? props.maxLength : \"\"}\r\n            required={props.required}\r\n            min={props.min}\r\n            max={props.max}\r\n            isInvalid={props.isInvalid}\r\n            onPaste={(e) =>\r\n              props.onlyChar ? e.preventDefault() : props.onChange\r\n            }\r\n            onWheel={props.onWheel}\r\n            step={props.step}\r\n            autoComplete={props.onlyChar ? props.autoComplete : \"off\"}\r\n            pattern=\"\\S(.*\\S)?\"\r\n            title={props.title ? props.title : props?.error?.props?.children}\r\n            onInvalid={props.onInvalid}\r\n            onInput={props.onInput}\r\n            className={`${props.inputtext} ${props.isError ? \"error-field\" : \"\"}`}\r\n            readOnly={props.readOnly}\r\n            onFocus={props.onFocus}\r\n            autoFocus={props.autoFocus}\r\n          />\r\n          {props.children}\r\n          {props.type === \"password\" ? (\r\n            <span onClick={() => setActive(!active)} className=\"eyeIcon\">\r\n              {active ? <EyeIcon /> : <CloseEye />}\r\n            </span>\r\n          ) : (\r\n            \"\"\r\n          )}\r\n        </div>\r\n        {props.error}\r\n        {props.smallText ? (\r\n          <Form.Text id=\"\" muted className=\"small-text-form\">\r\n            {props.smallText}\r\n\r\n          </Form.Text>\r\n        ) : (\r\n          \"\"\r\n        )}\r\n      </Form.Group>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;;AAMA,MAAM,YAAY,CAAC;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,8DAA8D,GAC9D,MAAM,qBAAqB;QAAC;QAAK;QAAK;QAAK;KAAI;IAC/C,MAAM,YAAY,CAAC;QACjB,IAAI,MAAM,cAAc,EAAE;YACxB,mBAAmB,IAAI,CAAC;QAC1B;QAEA,qEAAqE,GACrE,OAAO,MAAM,IAAI,KAAK,WAClB,CAAC,mBAAmB,QAAQ,CAAC,EAAE,GAAG,KACjC,EAAE,GAAG,KAAK,eACT,MAAM,SAAS,IACf,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,SAAS,AAAC,KAC9C,EAAE,cAAc,KACd,MAAM,QAAQ;IACpB;IAEA,qBACE;kBACE,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;YACT,WAAW,CAAC,YAAY,EAAE,MAAM,SAAS,EAAE;YAC3C,WAAW,MAAM,SAAS;;gBAEzB,MAAM,KAAK,iBACV,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,SAAS,MAAM,EAAE;oBAAE,WAAW,MAAM,UAAU;;wBACvD,MAAM,KAAK;sCACZ,6LAAC;sCAAI;;;;;;;;;;;2BAGP;8BAEF,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,aAAa,kBAClB,6LAAC;4BAAK,WAAU;;gCAAoB,MAAM,KAAK,CAAC,MAAM;gCAAC;;;;;;;sCAEzD,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;4BAEX,UAAU,MAAM,QAAQ;4BACxB,MAAM,MAAM,IAAI,KAAK,cAAc,SAAS,SAAS,MAAM,IAAI;4BAC/D,IAAI,MAAM,EAAE;4BACZ,MAAM,MAAM,IAAI;4BAChB,OAAO,MAAM,KAAK;4BAClB,wBAAwB;4BACxB,aAAa,MAAM,WAAW;4BAC9B,QAAQ,MAAM,MAAM;4BACpB,UAAU,MAAM,QAAQ;4BACxB,WAAW,MAAM,SAAS,GAAG,MAAM,SAAS,GAAG;4BAC/C,UAAU,MAAM,QAAQ;4BACxB,KAAK,MAAM,GAAG;4BACd,KAAK,MAAM,GAAG;4BACd,WAAW,MAAM,SAAS;4BAC1B,SAAS,CAAC,IACR,MAAM,QAAQ,GAAG,EAAE,cAAc,KAAK,MAAM,QAAQ;4BAEtD,SAAS,MAAM,OAAO;4BACtB,MAAM,MAAM,IAAI;4BAChB,cAAc,MAAM,QAAQ,GAAG,MAAM,YAAY,GAAG;4BACpD,SAAQ;4BACR,OAAO,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG,OAAO,OAAO,OAAO;4BACxD,WAAW,MAAM,SAAS;4BAC1B,SAAS,MAAM,OAAO;4BACtB,WAAW,GAAG,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,OAAO,GAAG,gBAAgB,IAAI;4BACrE,UAAU,MAAM,QAAQ;4BACxB,SAAS,MAAM,OAAO;4BACtB,WAAW,MAAM,SAAS;;;;;;wBAE3B,MAAM,QAAQ;wBACd,MAAM,IAAI,KAAK,2BACd,6LAAC;4BAAK,SAAS,IAAM,UAAU,CAAC;4BAAS,WAAU;sCAChD,uBAAS,6LAAC,uIAAA,CAAA,UAAO;;;;qDAAM,6LAAC,uIAAA,CAAA,WAAQ;;;;;;;;;mCAGnC;;;;;;;gBAGH,MAAM,KAAK;gBACX,MAAM,SAAS,iBACd,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;oBAAC,IAAG;oBAAG,KAAK;oBAAC,WAAU;8BAC9B,MAAM,SAAS;;;;;2BAIlB;;;;;;;;AAKV;GA1FM;KAAA;uCA4FS", "debugId": null}}, {"offset": {"line": 2590, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2596, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/AuthLayout.js"], "sourcesContent": ["import 'bootstrap/dist/css/bootstrap.min.css';\r\nimport { Container } from \"react-bootstrap\";\r\nimport \"../css/auth/authGlobals.scss\";\r\nimport \"../css/common/CommonButton.scss\"\r\n\r\n\r\n\r\nexport default function AuthLayout({ children }) {\r\n  return (\r\n    <div className=\"loginCommon\">\r\n      <Container fluid className=\"px-0\">\r\n        <main className=\"mx-0 d-flex flex-wrap\">\r\n          <div className=\"px-0 referralCol d-none d-lg-flex\">\r\n            <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-financial-tools.jpg\" alt=\"Access your TradeReply account – Log in, sign up, or recover credentials\" />\r\n          </div>\r\n          <div className=\"px-0 loginCol\">{children}</div>\r\n        </main>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;;;;;AAMe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,iMAAA,CAAA,YAAS;YAAC,KAAK;YAAC,WAAU;sBACzB,cAAA,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,KAAI;4BAA4E,KAAI;;;;;;;;;;;kCAE3F,6LAAC;wBAAI,WAAU;kCAAiB;;;;;;;;;;;;;;;;;;;;;;AAK1C;KAbwB", "debugId": null}}, {"offset": {"line": 2661, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2667, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/CommonButton.jsx"], "sourcesContent": ["import \"../../css/common/CommonButton.scss\";\r\n\r\n/**COMMON BUTTON WITH DYNAMIC PROPS */\r\n/** COMMON BUTTON WITH DYNAMIC PROPS */\r\nconst CommonButton = (props) => {\r\n\r\n  return (\r\n    <button\r\n      \r\n      onClick={props?.onClick}\r\n      type={props?.type}\r\n      className={`btn-style ${props.className} ${props.fluid ? \"w-100\" : \"\"} ${props.transparent ? \"transparent\" : \"\"} ${props.white20 ? \"white20\" : \"\"} ${props.whiteBtn ? \"white-btn\" : \"\"}`}\r\n      disabled={props?.disabled}\r\n    >\r\n      {props.onlyIcon && <span className=\"onlyIcon\">{props.onlyIcon}</span>}\r\n\r\n      <div className=\"d-flex flex-column align-items-center text-center\">\r\n        <span>{props.title}</span>\r\n        <span className=\"d-block\">{props.trial}</span>\r\n        <span className=\"d-block\">{props.subtitle}</span>\r\n        {props.innerText && (\r\n          <span style={{ fontSize: \"0.70em\", lineHeight: \"1\" }}>{props.innerText}</span>\r\n        )}\r\n      </div>\r\n\r\n      {props.btnIcon && (\r\n        <img\r\n          src={props.btnIcon}\r\n          alt={props?.title ? `${props.title} icon` : \"Button icon\"}\r\n          className=\"\"\r\n        />\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default CommonButton;"], "names": [], "mappings": ";;;;;;AAEA,oCAAoC,GACpC,qCAAqC,GACrC,MAAM,eAAe,CAAC;IAEpB,qBACE,6LAAC;QAEC,SAAS,OAAO;QAChB,MAAM,OAAO;QACb,WAAW,CAAC,UAAU,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,KAAK,GAAG,UAAU,GAAG,CAAC,EAAE,MAAM,WAAW,GAAG,gBAAgB,GAAG,CAAC,EAAE,MAAM,OAAO,GAAG,YAAY,GAAG,CAAC,EAAE,MAAM,QAAQ,GAAG,cAAc,IAAI;QACxL,UAAU,OAAO;;YAEhB,MAAM,QAAQ,kBAAI,6LAAC;gBAAK,WAAU;0BAAY,MAAM,QAAQ;;;;;;0BAE7D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAM,MAAM,KAAK;;;;;;kCAClB,6LAAC;wBAAK,WAAU;kCAAW,MAAM,KAAK;;;;;;kCACtC,6LAAC;wBAAK,WAAU;kCAAW,MAAM,QAAQ;;;;;;oBACxC,MAAM,SAAS,kBACd,6LAAC;wBAAK,OAAO;4BAAE,UAAU;4BAAU,YAAY;wBAAI;kCAAI,MAAM,SAAS;;;;;;;;;;;;YAIzE,MAAM,OAAO,kBACZ,6LAAC;gBACC,KAAK,MAAM,OAAO;gBAClB,KAAK,OAAO,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG;gBAC5C,WAAU;;;;;;;;;;;;AAKpB;KA9BM;uCAgCS", "debugId": null}}, {"offset": {"line": 2754, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2760, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/ThirdPartyLogin.jsx"], "sourcesContent": ["import { v4 as uuidv4 } from \"uuid\";\r\n\r\nconst ThirdPartyLogin = ({ type }) => {\r\n  const handleThirdPartyLogin = (provider) => {\r\n    if (type === \"signup\") {\r\n      const sessionKey = `signup_${provider}`;\r\n      let data = JSON.parse(sessionStorage.getItem(sessionKey) || \"{}\");\r\n\r\n      if (!data.uuid) {\r\n        const newUuid = uuidv4();\r\n        const expiresInMinutes = 15;\r\n        const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;\r\n\r\n        data = {\r\n          uuid: newUuid,\r\n          expiresAt,\r\n        };\r\n\r\n        sessionStorage.setItem(sessionKey, JSON.stringify(data));\r\n      }\r\n\r\n      const uuid = data.uuid;\r\n      \r\n      // Redirect to backend with UUID in query\r\n      window.location.href = `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/${provider}/registerationRedirect?uuid=${uuid}`;\r\n\r\n    } else {\r\n      window.location.href = `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/${provider}/loginRedirect`;\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"thirdParty_login d-flex justify-content-center\">\r\n      <button onClick={() => handleThirdPartyLogin(\"google\")} className=\"thirdParty_login_btn\">\r\n        <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-google-icon.svg\" alt=\"Continue with Google\" />\r\n      </button>\r\n      <button onClick={() => handleThirdPartyLogin(\"facebook\")} className=\"thirdParty_login_btn\">\r\n        <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-facebook-icon.svg\" alt=\"Continue with Facebook\" />\r\n      </button>\r\n      <button onClick={() => handleThirdPartyLogin(\"apple\")} className=\"thirdParty_login_btn\" disabled>\r\n        <img src=\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-apple-icon.svg\" alt=\"Continue with Apple\" />\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ThirdPartyLogin;\r\n"], "names": [], "mappings": ";;;AA2BgC;;AA3BhC;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,IAAI,EAAE;IAC/B,MAAM,wBAAwB,CAAC;QAC7B,IAAI,SAAS,UAAU;YACrB,MAAM,aAAa,CAAC,OAAO,EAAE,UAAU;YACvC,IAAI,OAAO,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,eAAe;YAE5D,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,MAAM,UAAU,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACrB,MAAM,mBAAmB;gBACzB,MAAM,YAAY,KAAK,GAAG,KAAK,mBAAmB,KAAK;gBAEvD,OAAO;oBACL,MAAM;oBACN;gBACF;gBAEA,eAAe,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YACpD;YAEA,MAAM,OAAO,KAAK,IAAI;YAEtB,yCAAyC;YACzC,OAAO,QAAQ,CAAC,IAAI,GAAG,2DAAwC,MAAM,EAAE,SAAS,4BAA4B,EAAE,MAAM;QAEtH,OAAO;YACL,OAAO,QAAQ,CAAC,IAAI,GAAG,2DAAwC,MAAM,EAAE,SAAS,cAAc,CAAC;QACjG;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,SAAS,IAAM,sBAAsB;gBAAW,WAAU;0BAChE,cAAA,6LAAC;oBAAI,KAAI;oBAA8E,KAAI;;;;;;;;;;;0BAE7F,6LAAC;gBAAO,SAAS,IAAM,sBAAsB;gBAAa,WAAU;0BAClE,cAAA,6LAAC;oBAAI,KAAI;oBAAgF,KAAI;;;;;;;;;;;0BAE/F,6LAAC;gBAAO,SAAS,IAAM,sBAAsB;gBAAU,WAAU;gBAAuB,QAAQ;0BAC9F,cAAA,6LAAC;oBAAI,KAAI;oBAA6E,KAAI;;;;;;;;;;;;;;;;;AAIlG;KA1CM;uCA4CS", "debugId": null}}, {"offset": {"line": 2856, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2862, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/LoginFooter.jsx"], "sourcesContent": ["// // import { <PERSON> } from \"@inertiajs/react\";\r\nimport Link from \"next/link\"; // Import Next.js Link\r\n\r\n\r\nconst LoginFooter = () => {\r\n  return (\r\n    <>\r\n      <div className=\"login_footer text-center\">\r\n        <div className=\"login_footer_links d-flex flex-wrap\">\r\n          <Link href=\"/privacy\" target=\"_blank\" rel=\"noopener noreferrer\">Privacy</Link>\r\n          <Link href=\"/terms\" target=\"_blank\" rel=\"noopener noreferrer\">Terms</Link>\r\n          <Link href=\"/disclaimer\" target=\"_blank\" rel=\"noopener noreferrer\">Disclaimer</Link>\r\n          <Link href=\"/cookies\" target=\"_blank\" rel=\"noopener noreferrer\">Cookies</Link>\r\n          <a href=\"#\" onClick={(e) => {\r\n            e.preventDefault(); // stop jumping\r\n            if (typeof Osano !== \"undefined\" && Osano.cm) {\r\n          \tOsano.cm.showDrawer(\"osano-cm-dom-info-dialog-open\");\r\n            }\r\n          }}\r\n          >\r\n          <PERSON><PERSON>\r\n          </a>\r\n        </div>\r\n        <p>Copyright © 2025 TradeReply. All Rights Reserved.</p>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default LoginFooter;\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;AAC9C,oRAA8B,sBAAsB;;;AAGpD,MAAM,cAAc;IAClB,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,QAAO;4BAAS,KAAI;sCAAsB;;;;;;sCAChE,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,QAAO;4BAAS,KAAI;sCAAsB;;;;;;sCAC9D,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAc,QAAO;4BAAS,KAAI;sCAAsB;;;;;;sCACnE,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,QAAO;4BAAS,KAAI;sCAAsB;;;;;;sCAChE,6LAAC;4BAAE,MAAK;4BAAI,SAAS,CAAC;gCACpB,EAAE,cAAc,IAAI,eAAe;gCACnC,IAAI,OAAO,UAAU,eAAe,MAAM,EAAE,EAAE;oCAC/C,MAAM,EAAE,CAAC,UAAU,CAAC;gCACnB;4BACF;sCACC;;;;;;;;;;;;8BAIH,6LAAC;8BAAE;;;;;;;;;;;;;AAIX;KAvBM;uCAyBS", "debugId": null}}, {"offset": {"line": 2960, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2966, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/validations/errorMessages.js"], "sourcesContent": ["export const errorMessages = {\r\n  NAME_NO_LEADING_SPACES: \"Name cannot start with a space.\",\r\n  NAME_ONLY_LETTERS: \"Name can only contain letters and spaces.\",\r\n  NAME_REQUIRED: \"This field is required.\",\r\n  EMAIL_INVALID: \"Please enter a valid email address.\",\r\n  EMAIL_REQUIRED: \"This field is required.\",\r\n  PASSWORD_REQUIRED: \"This field is required.\",\r\n  PASSWORD_MIN_LENGTH: \"Must be at least 8 characters long and include letters, numbers, and symbols. Spaces are not allowed.\",\r\n  PASSWORD_UPPERCASE: \"Password must contain at least one uppercase letter. Spaces are not allowed.\",\r\n  PASSWORD_LOWERCASE: \"Password must contain at least one lowercase letter. Spaces are not allowed.\",\r\n  PASSWORD_NUMBER: \"Password must contain at least one number. Spaces are not allowed.\",\r\n  PASSWORD_SPECIAL_CHAR:\r\n    \"Password must contain at least one special character. Spaces are not allowed.\",\r\n  PASSWORD_CONFIRMATION: \"Passwords do not match. Please try again.\",\r\n  PASSWORD_CONFIRMATION_REQUIRED: \"This field is required.\"\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,gBAAgB;IAC3B,wBAAwB;IACxB,mBAAmB;IACnB,eAAe;IACf,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,qBAAqB;IACrB,oBAAoB;IACpB,oBAAoB;IACpB,iBAAiB;IACjB,uBACE;IACF,uBAAuB;IACvB,gCAAgC;AAClC", "debugId": null}}, {"offset": {"line": 2987, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2993, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/validations/schema.js"], "sourcesContent": ["import * as Yup from \"yup\";\r\nimport { errorMessages } from \"./errorMessages\";\r\n\r\nexport const loginSchema = Yup.object({\r\n  email: Yup.string()\r\n    .email(errorMessages.EMAIL_INVALID)\r\n    .matches(/\\.[a-zA-Z]{2,4}$/, errorMessages.EMAIL_INVALID)\r\n    .required(errorMessages.EMAIL_REQUIRED),\r\n  password: Yup.string()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n  // .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n  // .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n  // .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n  // .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n  // .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n});\r\n\r\nexport const signupSchema = Yup.object({\r\n  email: Yup.string()\r\n    .trim()\r\n    .email(errorMessages.EMAIL_INVALID)\r\n    .required(errorMessages.EMAIL_REQUIRED)\r\n    .matches(/\\.[a-zA-Z]{2,4}$/, errorMessages.EMAIL_INVALID),\r\n  password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n    .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n  password_confirmation: Yup.string()\r\n    .trim()\r\n    .oneOf([Yup.ref('password'), null], errorMessages.PASSWORD_CONFIRMATION)\r\n    .required(errorMessages.PASSWORD_CONFIRMATION_REQUIRED),\r\n});\r\n\r\nexport const changePasswordSchema = Yup.object({\r\n  new_password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n    .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n  confirm_new_password: Yup.string()\r\n    .trim()\r\n    .oneOf([Yup.ref('new_password'), null], errorMessages.PASSWORD_CONFIRMATION)\r\n    .required(errorMessages.PASSWORD_CONFIRMATION_REQUIRED),\r\n});\r\n\r\nexport const localAccountSchema = Yup.object({\r\n  emailOrUsername: Yup.string()\r\n    .test(\"is-email-or-username\", \"Enter a valid email or username\", (value) => {\r\n      if (!value) return false; // Reject empty input\r\n\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/; // Standard email pattern\r\n      const usernameRegex = /^[a-zA-Z0-9_.]+$/; // Allows letters, numbers, underscores, and dots\r\n\r\n      return emailRegex.test(value) || usernameRegex.test(value);\r\n    })\r\n    .required(\"Email or username is required\"),\r\n});\r\n\r\n\r\nexport const forgetSchema = Yup.object({\r\n  email: Yup.string()\r\n    .email(errorMessages.EMAIL_INVALID)\r\n    .required(errorMessages.EMAIL_REQUIRED),\r\n});\r\n\r\n\r\nexport const createUsernameSchema = Yup.object().shape({\r\n  name: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"User name cannot exceed 100 characters\"),\r\n  // .test(\"unique\", \"This username is already taken. Please choose another one.\"),\r\n});\r\nexport const securitySchema = Yup.object().shape({\r\n  security_code: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n});\r\nexport const resetSchema = Yup.object({\r\n  password: Yup.string()\r\n    .trim()\r\n    .required(errorMessages.PASSWORD_REQUIRED)\r\n    .min(8, errorMessages.PASSWORD_MIN_LENGTH)\r\n    .matches(/[A-Z]/, errorMessages.PASSWORD_UPPERCASE)\r\n    .matches(/[a-z]/, errorMessages.PASSWORD_LOWERCASE)\r\n    .matches(/[0-9]/, errorMessages.PASSWORD_NUMBER)\r\n    .matches(/[!@#$%^&*]/, errorMessages.PASSWORD_SPECIAL_CHAR),\r\n  password_confirmation: Yup.string()\r\n    .oneOf([Yup.ref(\"password\")], \"Passwords must match\")\r\n    .required(\"Password confirmation is required\"),\r\n});\r\n\r\nexport const blogValidationSchema = Yup.object().shape({\r\n  title: Yup.string()\r\n    .trim()\r\n    .required(\"Title is required\")\r\n    .min(5, \"Title must be at least 5 characters\")\r\n    .max(100, \"Title cannot exceed 100 characters\"),\r\n  content: Yup.string()\r\n    .trim()\r\n    .required(\"Content is required\")\r\n    .min(50, \"Content must be at least 20 characters\")\r\n    .max(1000, \"Content cannot exceed 100 characters\"),\r\n});\r\n\r\nexport const titleDescValidationSchema = Yup.object().shape({\r\n  title: Yup.string()\r\n    .trim()\r\n    .required(\"Title is required\")\r\n    .min(5, \"Title must be at least 5 characters\")\r\n    .max(100, \"Title cannot exceed 100 characters\"),\r\n  content: Yup.string()\r\n    .trim()\r\n    .required(\"Content is required\")\r\n    .min(50, \"Content must be at least 20 characters\")\r\n    .max(1000, \"Content cannot exceed 100 characters\"),\r\n});\r\n\r\nexport const ArticleSchema = Yup.object({\r\n  primary_category_id: Yup.string().required(\"Primary Category is required\"),\r\n  // secondary_category_id: Yup.array()\r\n  //   .of(Yup.string())\r\n  //   .min(1, \"Select at least one secondary category\")\r\n  //   .required(\"Secondary Category is required\"),\r\n  title: Yup.string().required(\"Page Title is required\"),\r\n  title: Yup.string().required(\"Image URL is required\"),\r\n\r\n  content: Yup.string().required(\"Body Text is required\"),\r\n  summary: Yup.string()\r\n    .max(250, \"Max 250 characters allowed\")\r\n    .required(\"Page Summary is required\"),\r\n});\r\nexport const checkoutSchema = Yup.object({\r\n  firstName: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"First name cannot exceed 100 characters\"),\r\n  lastName: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"Last name cannot exceed 100 characters\"),\r\n  country: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\"),\r\n  address: Yup.string()\r\n    .trim()\r\n    .required(\"This field is required\")\r\n    .max(100, \"Address cannot exceed 100 characters\"),\r\n  // cardNumber: Yup.string()\r\n  //   .matches(/^\\d{4} \\d{4} \\d{4} \\d{4}$/, \"Card number must be in format 1234 5678 9012 3456\")\r\n  //   .required(\"Card number is required\"),\r\n  // expireDate: Yup.string()\r\n  //   .matches(/^(0[1-9]|1[0-2]) \\/ \\d{4}$/, \"Date must be in MM / YYYY format\")\r\n  //   .required(\"This field is required\"),\r\n\r\n  // securityCode: Yup.string()\r\n  //   .matches(/^\\d{3}$/, \"Code must be 3 digits\")\r\n  //   .required(\"This field is required\"),\r\n});"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,MAAM,cAAc,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,EAAE;IACpC,OAAO,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACb,KAAK,CAAC,sIAAA,CAAA,gBAAa,CAAC,aAAa,EACjC,OAAO,CAAC,oBAAoB,sIAAA,CAAA,gBAAa,CAAC,aAAa,EACvD,QAAQ,CAAC,sIAAA,CAAA,gBAAa,CAAC,cAAc;IACxC,UAAU,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAChB,QAAQ,CAAC,sIAAA,CAAA,gBAAa,CAAC,iBAAiB;AAM7C;AAEO,MAAM,eAAe,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,EAAE;IACrC,OAAO,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACb,IAAI,GACJ,KAAK,CAAC,sIAAA,CAAA,gBAAa,CAAC,aAAa,EACjC,QAAQ,CAAC,sIAAA,CAAA,gBAAa,CAAC,cAAc,EACrC,OAAO,CAAC,oBAAoB,sIAAA,CAAA,gBAAa,CAAC,aAAa;IAC1D,UAAU,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAChB,IAAI,GACJ,QAAQ,CAAC,sIAAA,CAAA,gBAAa,CAAC,iBAAiB,EACxC,GAAG,CAAC,GAAG,sIAAA,CAAA,gBAAa,CAAC,mBAAmB,EACxC,OAAO,CAAC,SAAS,sIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,sIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,sIAAA,CAAA,gBAAa,CAAC,eAAe,EAC9C,OAAO,CAAC,cAAc,sIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5D,uBAAuB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAC7B,IAAI,GACJ,KAAK,CAAC;QAAC,CAAA,GAAA,sIAAA,CAAA,MAAO,AAAD,EAAE;QAAa;KAAK,EAAE,sIAAA,CAAA,gBAAa,CAAC,qBAAqB,EACtE,QAAQ,CAAC,sIAAA,CAAA,gBAAa,CAAC,8BAA8B;AAC1D;AAEO,MAAM,uBAAuB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,EAAE;IAC7C,cAAc,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACpB,IAAI,GACJ,QAAQ,CAAC,sIAAA,CAAA,gBAAa,CAAC,iBAAiB,EACxC,GAAG,CAAC,GAAG,sIAAA,CAAA,gBAAa,CAAC,mBAAmB,EACxC,OAAO,CAAC,SAAS,sIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,sIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,sIAAA,CAAA,gBAAa,CAAC,eAAe,EAC9C,OAAO,CAAC,cAAc,sIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5D,sBAAsB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAC5B,IAAI,GACJ,KAAK,CAAC;QAAC,CAAA,GAAA,sIAAA,CAAA,MAAO,AAAD,EAAE;QAAiB;KAAK,EAAE,sIAAA,CAAA,gBAAa,CAAC,qBAAqB,EAC1E,QAAQ,CAAC,sIAAA,CAAA,gBAAa,CAAC,8BAA8B;AAC1D;AAEO,MAAM,qBAAqB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,EAAE;IAC3C,iBAAiB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACvB,IAAI,CAAC,wBAAwB,mCAAmC,CAAC;QAChE,IAAI,CAAC,OAAO,OAAO,OAAO,qBAAqB;QAE/C,MAAM,aAAa,8BAA8B,yBAAyB;QAC1E,MAAM,gBAAgB,oBAAoB,iDAAiD;QAE3F,OAAO,WAAW,IAAI,CAAC,UAAU,cAAc,IAAI,CAAC;IACtD,GACC,QAAQ,CAAC;AACd;AAGO,MAAM,eAAe,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,EAAE;IACrC,OAAO,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACb,KAAK,CAAC,sIAAA,CAAA,gBAAa,CAAC,aAAa,EACjC,QAAQ,CAAC,sIAAA,CAAA,gBAAa,CAAC,cAAc;AAC1C;AAGO,MAAM,uBAAuB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;IACrD,MAAM,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACZ,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;AAEd;AACO,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;IAC/C,eAAe,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACrB,IAAI,GACJ,QAAQ,CAAC;AACd;AACO,MAAM,cAAc,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,EAAE;IACpC,UAAU,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAChB,IAAI,GACJ,QAAQ,CAAC,sIAAA,CAAA,gBAAa,CAAC,iBAAiB,EACxC,GAAG,CAAC,GAAG,sIAAA,CAAA,gBAAa,CAAC,mBAAmB,EACxC,OAAO,CAAC,SAAS,sIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,sIAAA,CAAA,gBAAa,CAAC,kBAAkB,EACjD,OAAO,CAAC,SAAS,sIAAA,CAAA,gBAAa,CAAC,eAAe,EAC9C,OAAO,CAAC,cAAc,sIAAA,CAAA,gBAAa,CAAC,qBAAqB;IAC5D,uBAAuB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAC7B,KAAK,CAAC;QAAC,CAAA,GAAA,sIAAA,CAAA,MAAO,AAAD,EAAE;KAAY,EAAE,wBAC7B,QAAQ,CAAC;AACd;AAEO,MAAM,uBAAuB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;IACrD,OAAO,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACb,IAAI,GACJ,QAAQ,CAAC,qBACT,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK;IACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACf,IAAI,GACJ,QAAQ,CAAC,uBACT,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,MAAM;AACf;AAEO,MAAM,4BAA4B,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;IAC1D,OAAO,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACb,IAAI,GACJ,QAAQ,CAAC,qBACT,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK;IACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACf,IAAI,GACJ,QAAQ,CAAC,uBACT,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,MAAM;AACf;AAEO,MAAM,gBAAgB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,EAAE;IACtC,qBAAqB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC;IAC3C,qCAAqC;IACrC,sBAAsB;IACtB,sDAAsD;IACtD,iDAAiD;IACjD,OAAO,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC;IAC7B,OAAO,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC;IAE7B,SAAS,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC;IAC/B,SAAS,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACf,GAAG,CAAC,KAAK,8BACT,QAAQ,CAAC;AACd;AACO,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,EAAE;IACvC,WAAW,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACjB,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;IACZ,UAAU,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAChB,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;IACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACf,IAAI,GACJ,QAAQ,CAAC;IACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IACf,IAAI,GACJ,QAAQ,CAAC,0BACT,GAAG,CAAC,KAAK;AAWd", "debugId": null}}, {"offset": {"line": 3081, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3087, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/NavLink.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\nexport default function NavLink({ className = \"\", children, href, ...props }) {\r\n  const pathname = usePathname();\r\n\r\n  const isActive = pathname === href;\r\n  return (\r\n    <Link\r\n      href={href}\r\n      {...props}\r\n      className={`${className} ${isActive ? \"active\" : \"\"}`}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS,QAAQ,EAAE,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO;;IAC1E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,aAAa;IAC9B,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACL,GAAG,KAAK;QACT,WAAW,GAAG,UAAU,CAAC,EAAE,WAAW,WAAW,IAAI;kBAEpD;;;;;;AAGP;GAbwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 3124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Meta/MetaHead.js"], "sourcesContent": ["export default function MetaHead({ props, children }) {\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n\r\n  const defaultMeta = {\r\n    title: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    description: \"Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.\",\r\n    canonical: \"https://www.tradereply.com/\",\r\n    ogTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    ogDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    ogSiteName: \"TradeReply\",\r\n    ogImage: \"https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg\",\r\n    twitterTitle: \"TradeReply: Optimize Your Trading Strategies & Analytics\",\r\n    twitterDescription: \"Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.\",\r\n    twitterImage: 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'\r\n  };\r\n  const isNoIndex = props?.noindex === true;\r\n  const robotsContent = props?.robots || (isNoIndex ? \"noindex, follow\" : \"index, follow\");\r\n\r\n  return (\r\n    <>\r\n\r\n      {children}\r\n      <title>{props?.title || defaultMeta.title}</title>\r\n\r\n      {/* Always render robots tag */}\r\n      {/* <meta\r\n        name=\"robots\"\r\n        content={isNoIndex ? \"noindex, follow\" : \"index, follow\"}\r\n      /> */}\r\n            <meta name=\"robots\" content={robotsContent} />\r\n      {props?.canonical_link?.trim() && props?.noindex !== true && (\r\n        <link rel=\"canonical\" href={props.canonical_link} />\r\n      )}\r\n\r\n      <meta\r\n        name=\"description\"\r\n        content={props?.description || defaultMeta.description}\r\n      />\r\n      \r\n      {props?.rel_next && (\r\n        <link rel=\"next\" href={props?.rel_next} />\r\n      )}  \r\n\r\n      <meta property=\"og:title\" content={props?.og_title || defaultMeta?.ogTitle} />\r\n      <meta property=\"og:description\" content={props?.og_description || defaultMeta?.ogDescription} />\r\n      <meta property=\"og:site_name\" content={props?.og_site_name || defaultMeta?.ogSiteName} />\r\n\r\n      <meta property=\"og:image\" content=\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\" />\r\n      <meta property=\"og:type\" content=\"website\" />\r\n\r\n      <meta property=\"og:image:width\" content=\"1200\" />\r\n      <meta property=\"og:image:height\" content=\"630\" />\r\n      <meta property=\"og:locale\" content=\"en_US\" />\r\n\r\n      {/*/!* Twitter Meta Tags *!/*/}\r\n      <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n      <meta name=\"twitter:title\" content={props?.twitter_title || defaultMeta?.twitterTitle} />\r\n      <meta name=\"twitter:description\" content={props?.twitter_description || defaultMeta?.twitterDescription} />\r\n      <meta name=\"twitter:image\" content=\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\" />\r\n      <meta name=\"twitter:site\" content=\"@JoinTradeReply\" />\r\n\r\n      {/* Favicon */}\r\n      <link rel=\"icon\" type=\"image/x-icon\" href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`} />\r\n      <link rel=\"icon\" type=\"image/svg+xml\" href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`} />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AACsB;;;AADP,SAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;IAClD,MAAM;IAEN,MAAM,cAAc;QAClB,OAAO;QACP,aAAa;QACb,WAAW;QACX,SAAS;QACT,eAAe;QACf,YAAY;QACZ,SAAS;QACT,cAAc;QACd,oBAAoB;QACpB,cAAc;IAChB;IACA,MAAM,YAAY,OAAO,YAAY;IACrC,MAAM,gBAAgB,OAAO,UAAU,CAAC,YAAY,oBAAoB,eAAe;IAEvF,qBACE;;YAEG;0BACD,6LAAC;0BAAO,OAAO,SAAS,YAAY,KAAK;;;;;;0BAOnC,6LAAC;gBAAK,MAAK;gBAAS,SAAS;;;;;;YAClC,OAAO,gBAAgB,UAAU,OAAO,YAAY,sBACnD,6LAAC;gBAAK,KAAI;gBAAY,MAAM,MAAM,cAAc;;;;;;0BAGlD,6LAAC;gBACC,MAAK;gBACL,SAAS,OAAO,eAAe,YAAY,WAAW;;;;;;YAGvD,OAAO,0BACN,6LAAC;gBAAK,KAAI;gBAAO,MAAM,OAAO;;;;;;0BAGhC,6LAAC;gBAAK,UAAS;gBAAW,SAAS,OAAO,YAAY,aAAa;;;;;;0BACnE,6LAAC;gBAAK,UAAS;gBAAiB,SAAS,OAAO,kBAAkB,aAAa;;;;;;0BAC/E,6LAAC;gBAAK,UAAS;gBAAe,SAAS,OAAO,gBAAgB,aAAa;;;;;;0BAE3E,6LAAC;gBAAK,UAAS;gBAAW,SAAQ;;;;;;0BAClC,6LAAC;gBAAK,UAAS;gBAAU,SAAQ;;;;;;0BAEjC,6LAAC;gBAAK,UAAS;gBAAiB,SAAQ;;;;;;0BACxC,6LAAC;gBAAK,UAAS;gBAAkB,SAAQ;;;;;;0BACzC,6LAAC;gBAAK,UAAS;gBAAY,SAAQ;;;;;;0BAGnC,6LAAC;gBAAK,MAAK;gBAAe,SAAQ;;;;;;0BAClC,6LAAC;gBAAK,MAAK;gBAAgB,SAAS,OAAO,iBAAiB,aAAa;;;;;;0BACzE,6LAAC;gBAAK,MAAK;gBAAsB,SAAS,OAAO,uBAAuB,aAAa;;;;;;0BACrF,6LAAC;gBAAK,MAAK;gBAAgB,SAAQ;;;;;;0BACnC,6LAAC;gBAAK,MAAK;gBAAe,SAAQ;;;;;;0BAGlC,6LAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAe,MAAM,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;;;;;;0BACzH,6LAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAgB,MAAM,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;;;;;;;;AAGhI;KAlEwB", "debugId": null}}, {"offset": {"line": 3325, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3331, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/AuthLogo.js"], "sourcesContent": ["import React from 'react'\r\n\r\nexport default function AuthLogo() {\r\n    return (\r\n        <>\r\n            <div className=\"d-flex justify-content-center mb-4 pb-xl-2\">\r\n                <img src=\"https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg\" alt=\"Brand Logo\" />\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACpB,qBACI;kBACI,cAAA,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAI,KAAI;gBAAkF,KAAI;;;;;;;;;;;;AAI/G;KARwB", "debugId": null}}, {"offset": {"line": 3363, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/common/Auth/PasswordValidation.js"], "sourcesContent": ["import React from 'react'\r\nimport { Row, Col } from \"react-bootstrap\";\r\n\r\nexport default function PasswordValidation({ password, isValid }) {\r\n    const trimmedPassword = password.replace(/\\s/g, \"\");\r\n    const length = trimmedPassword.length;\r\n    const isLongEnough = length >= 8;\r\n    const hasUppercase = /[A-Z]/.test(trimmedPassword);\r\n    const hasLowercase = /[a-z]/.test(trimmedPassword);\r\n    const hasUppercaseLowercase = hasUppercase && hasLowercase;\r\n    const hasNumber = /\\d/.test(trimmedPassword);\r\n    const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(trimmedPassword);\r\n\r\n    let requirements = [];\r\n    let includeParts = [];\r\n\r\n    if (!isLongEnough) requirements.push(\"be at least 8 characters long\");\r\n    if (!hasUppercase) includeParts.push(\"one uppercase letter\");\r\n    if (!hasLowercase) includeParts.push(\"one lowercase letter\");\r\n    if (!hasNumber) includeParts.push(\"number\");\r\n    if (!hasSpecialChar) includeParts.push(\"symbol\");\r\n\r\n    if (includeParts.length > 0) {\r\n        requirements.push(\"include \" + includeParts.join(\", \"));\r\n    }\r\n\r\n    let errorMessage = \"\";\r\n    if (requirements.length > 0 || password.includes(\" \")) {\r\n        errorMessage = \"Password must \" + requirements.join(\", \") + \". Spaces are not allowed.\";\r\n    }\r\n\r\n    const conditionsMet = [isLongEnough, hasUppercaseLowercase, hasNumber, hasSpecialChar].filter(Boolean).length\r\n    return (\r\n        <>\r\n            <div className=\"mb-3 password_check\">\r\n                {errorMessage && <span className=\"error-message pt-0 pb-1\">{errorMessage}</span>}\r\n                {conditionsMet === 0 && (\r\n                    <div className=\"box1\">\r\n                        <Row className=\"mt-2\">\r\n                            <Col><div className=\"box1_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                        </Row>\r\n                    </div>\r\n                )}\r\n                {conditionsMet === 1 && (\r\n                    <div className=\"box2\">\r\n                        <Row className=\"mt-2\">\r\n                            <Col><div className=\"box1_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box2_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                        </Row>\r\n                    </div>\r\n                )}\r\n                {conditionsMet === 2 && (\r\n                    <div className=\"box3\">\r\n                        <Row className=\"mt-2\">\r\n                            <Col><div className=\"box1_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box2_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box3_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                        </Row>\r\n                    </div>\r\n                )}\r\n                {conditionsMet === 3 && (\r\n                    <div className=\"box4\">\r\n                        <Row className=\"mt-2\">\r\n                            <Col><div className=\"box1_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box2_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box3_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box4_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"white10_bg p-1 rounded-5\"></div></Col>\r\n                        </Row>\r\n                    </div>\r\n                )}\r\n\r\n                {conditionsMet === 4 || isValid && (\r\n                    <div className=\"box5\">\r\n                        <Row className=\"mt-2\">\r\n                            <Col><div className=\"box1_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box2_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box3_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box4_bg p-1 rounded-5\"></div></Col>\r\n                            <Col><div className=\"box5_bg p-1 rounded-5\"></div></Col>\r\n                        </Row>\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEe,SAAS,mBAAmB,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC5D,MAAM,kBAAkB,SAAS,OAAO,CAAC,OAAO;IAChD,MAAM,SAAS,gBAAgB,MAAM;IACrC,MAAM,eAAe,UAAU;IAC/B,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,wBAAwB,gBAAgB;IAC9C,MAAM,YAAY,KAAK,IAAI,CAAC;IAC5B,MAAM,iBAAiB,yBAAyB,IAAI,CAAC;IAErD,IAAI,eAAe,EAAE;IACrB,IAAI,eAAe,EAAE;IAErB,IAAI,CAAC,cAAc,aAAa,IAAI,CAAC;IACrC,IAAI,CAAC,cAAc,aAAa,IAAI,CAAC;IACrC,IAAI,CAAC,cAAc,aAAa,IAAI,CAAC;IACrC,IAAI,CAAC,WAAW,aAAa,IAAI,CAAC;IAClC,IAAI,CAAC,gBAAgB,aAAa,IAAI,CAAC;IAEvC,IAAI,aAAa,MAAM,GAAG,GAAG;QACzB,aAAa,IAAI,CAAC,aAAa,aAAa,IAAI,CAAC;IACrD;IAEA,IAAI,eAAe;IACnB,IAAI,aAAa,MAAM,GAAG,KAAK,SAAS,QAAQ,CAAC,MAAM;QACnD,eAAe,mBAAmB,aAAa,IAAI,CAAC,QAAQ;IAChE;IAEA,MAAM,gBAAgB;QAAC;QAAc;QAAuB;QAAW;KAAe,CAAC,MAAM,CAAC,SAAS,MAAM;IAC7G,qBACI;kBACI,cAAA,6LAAC;YAAI,WAAU;;gBACV,8BAAgB,6LAAC;oBAAK,WAAU;8BAA2B;;;;;;gBAC3D,kBAAkB,mBACf,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;wBAAC,WAAU;;0CACX,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAI/B,kBAAkB,mBACf,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;wBAAC,WAAU;;0CACX,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAI/B,kBAAkB,mBACf,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;wBAAC,WAAU;;0CACX,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAI/B,kBAAkB,mBACf,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;wBAAC,WAAU;;0CACX,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAK/B,kBAAkB,KAAK,yBACpB,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,qLAAA,CAAA,MAAG;wBAAC,WAAU;;0CACX,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CACpB,6LAAC,qLAAA,CAAA,MAAG;0CAAC,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD;KA5FwB", "debugId": null}}, {"offset": {"line": 3839, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3845, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/hashInput.js"], "sourcesContent": ["export function hashInput(input) {\r\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\r\n  \r\n    if (isEmail) {\r\n      return input.replace(/^(.)(.*)(@.*)$/, (_, first, middle, domain) => `**${middle.slice(-1)}${domain}`);\r\n    } else {\r\n      return input.length > 3\r\n        ? `**${input.slice(-2)}` // Show last 2 characters for usernames\r\n        : `**`; // Mask completely if too short\r\n    }\r\n  }\r\n  "], "names": [], "mappings": ";;;AAAO,SAAS,UAAU,KAAK;IAC3B,MAAM,UAAU,6BAA6B,IAAI,CAAC;IAElD,IAAI,SAAS;QACX,OAAO,MAAM,OAAO,CAAC,kBAAkB,CAAC,GAAG,OAAO,QAAQ,SAAW,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,CAAC,KAAK,QAAQ;IACvG,OAAO;QACL,OAAO,MAAM,MAAM,GAAG,IAClB,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,uCAAuC;WAC9D,CAAC,EAAE,CAAC,EAAE,+BAA+B;IAC3C;AACF", "debugId": null}}, {"offset": {"line": 3860, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3866, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Auth%29/signup/page.js"], "sourcesContent": ["\"use client\";\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport InputError from \"@/Components/UI/InputError\";\r\nimport TextInput from \"@/Components/UI/TextInput\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport AuthLayout from \"@/Layouts/AuthLayout\";\r\nimport CommonButton from \"@/Components/UI/CommonButton\";\r\nimport { RightArrowIconSvg, CheckIcon, RedErrorCircle } from \"@/assets/svgIcons/SvgIcon\";\r\nimport ThirdPartyLogin from \"@/Components/common/ThirdPartyLogin\";\r\nimport LoginFooter from \"@/Components/UI/LoginFooter\";\r\nimport { signupSchema } from \"@/validations/schema\";\r\nimport NavLink from \"@/Components/UI/NavLink\";\r\nimport { Formik, Field, Form, ErrorMessage } from \"formik\";\r\nimport axios from \"axios\";\r\nimport { register } from \"@/utils/auth\";\r\nimport toast from \"react-hot-toast\";\r\nimport MetaHead from \"@/Seo/Meta/MetaHead\";\r\nimport AuthLogo from \"@/Components/common/AuthLogo\";\r\nimport PasswordValidation from \"@/Components/common/Auth/PasswordValidation\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport { hashInput } from \"@/utils/hashInput\";\r\n\r\n\r\nconst initialValues = {\r\n  email: \"\",\r\n  password: \"\",\r\n  password_confirmation: \"\"\r\n};\r\n\r\nexport default function Register() {\r\n  const router = useRouter();\r\n  const [authError, setAuthError] = useState(\"\");\r\n  const [showPasswordCheck, setShowPasswordCheck] = useState(false);\r\n  const [password, setPassword] = useState(\"\");\r\n  const [isFocused, setIsFocused] = useState(false);\r\n  const uuid = uuidv4(); // Generate a unique identifier\r\n  useEffect(() => {\r\n    sessionStorage.removeItem(\"masked_email\");\r\n    sessionStorage.removeItem(\"identifier_type\");\r\n    sessionStorage.removeItem(\"signup_data\");\r\n    sessionStorage.removeItem(\"signup_facebook\");\r\n    sessionStorage.removeItem(\"signup_google\");\r\n    sessionStorage.removeItem(\"reset_password_data\");\r\n    const savedData = JSON.parse(sessionStorage.getItem(\"signup_data\"));\r\n\r\n    if (!savedData || Date.now() > savedData.expiry) {\r\n      sessionStorage.removeItem(\"signup_data\");\r\n      router.push(\"/signup\");\r\n    }\r\n  }, []);\r\n\r\n  const handleSubmit = async (values, { setErrors, resetForm }) => {\r\n    const { email, password } = values;\r\n    const expiresInMinutes = 15;\r\n    const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;\r\n\r\n    const allowedPricings = [\"free\", \"pro\", \"premium\", \"essential\"];\r\n\r\n    const pricing = allowedPricings.includes(sessionStorage.getItem(\"pricing\"))\r\n      ? sessionStorage.getItem(\"pricing\")\r\n      : \"free\";\r\n\r\n    const trial = sessionStorage.getItem(\"trial\") === \"true\";\r\n\r\n    try {\r\n      const response = await register({ email, password, uuid, pricing, trial });\r\n\r\n      if (!response.success) {\r\n        setAuthError(response.errors.email?.[0]);\r\n        return;\r\n      }\r\n      setAuthError(\"\");\r\n      sessionStorage.removeItem(\"pricing\");\r\n      sessionStorage.removeItem(\"trial\");\r\n      sessionStorage.setItem(\"masked_email\", hashInput(values.email));\r\n      sessionStorage.setItem(\r\n        \"signup_data\",\r\n        JSON.stringify({ uuid, expiresAt })\r\n      );\r\n\r\n      router.push(`/security-check?signup=signup_data`);\r\n    } catch (error) {\r\n      resetForm();\r\n      setAuthError(\"An unexpected error occurred.\");\r\n    }\r\n  };\r\n\r\n\r\n  const metaArray = {\r\n    noindex: true,\r\n    title: \"Sign Up for TradeReply | Start Trading Smarter\",\r\n    description: \"Join TradeReply.com today! Sign up to unlock advanced trading tools, real-time analytics, and expert strategies to enhance your trading success.\",\r\n    canonical_link: \"https://www.tradereply.com/signup\",\r\n    og_site_name: \"TradeReply\",\r\n    og_title: \"Sign Up for TradeReply | Start Trading Smarter\",\r\n    og_description: \"Join TradeReply.com today! Sign up to unlock advanced trading tools, real-time analytics, and expert strategies to enhance your trading success.\",\r\n    twitter_title: \"Sign Up for TradeReply | Start Trading Smarter\",\r\n    twitter_description: \"Join TradeReply.com today! Sign up to unlock advanced trading tools, real-time analytics, and expert strategies to enhance your trading success.\",\r\n  };\r\n\r\n  return (\r\n    <AuthLayout>\r\n      <MetaHead props={metaArray} />\r\n      <div className=\"loginCommon_rightSide signup_form\">\r\n        <div className=\"loginCommon_rightSide_inner\">\r\n          <div className=\"backbtn\">\r\n            <Link href={\"/\"}>\r\n              <RightArrowIconSvg color=\"svg-white_baseblue\" /> Return to Home\r\n            </Link>\r\n          </div>\r\n          <div className=\"loginCommon_rightSide_formBox\">\r\n            <AuthLogo />\r\n            <div className=\"loginHeading\">\r\n              <h1>Sign up with</h1>\r\n            </div>\r\n            <ThirdPartyLogin type=\"signup\" />\r\n            <div className=\"orLine\">\r\n              <span>or start with</span>\r\n            </div>\r\n            <div className=\"loginTabs\">\r\n              <div className=\"loginForm\">\r\n                <Formik\r\n                  initialValues={initialValues}\r\n                  validationSchema={signupSchema}\r\n                  onSubmit={handleSubmit}\r\n                >\r\n                  {({\r\n                    handleChange,\r\n                    values,\r\n                    errors,\r\n                    touched,\r\n                    isSubmitting,\r\n                    resetForm,\r\n                    dirty,\r\n                    isValid,\r\n                    submitCount\r\n                  }) => (\r\n                    <Form>\r\n                      <div className=\"authCorrectIcon\">\r\n                        <div className=\"checkIcon\">\r\n                          {values.email && !errors.email && dirty && <CheckIcon width=\"25\" height=\"25\" />}\r\n                        </div>\r\n                        <Field name=\"email\">\r\n                          {({ field, meta }) => (\r\n                            <TextInput\r\n                              {...field}\r\n                              placeholder=\"Email\"\r\n                              type=\"text\"\r\n                              maxLength={100}\r\n                              error={meta.touched && meta.error ? <InputError message={meta.error} /> : null}\r\n                              isError={meta.touched && meta.error ? true : false}\r\n                              onChange={(e) => {\r\n                                field.onChange(e);\r\n                                setAuthError(null);\r\n                              }}\r\n                            />\r\n                          )}\r\n                        </Field>\r\n                      </div>\r\n\r\n                      <div className=\"authCorrectIcon\">\r\n                        <div className=\"checkIcon\">\r\n                          {values.password && !errors.password && dirty && <CheckIcon width=\"25\" height=\"25\" />}\r\n                        </div>\r\n                        <Field name=\"password\">\r\n                          {({ field, meta, form: { touched, errors } }) => (\r\n                            <>\r\n                              <TextInput\r\n                                {...field}\r\n                                placeholder=\"Password\"\r\n                                type=\"password\"\r\n                                maxLength={64}\r\n                                onChange={(e) => {\r\n                                  const cleanedValue = e.target.value.replace(/\\s/g, \"\");\r\n                                  field.onChange({ target: { name: e.target.name, value: cleanedValue } });\r\n                                  setPassword(cleanedValue);\r\n                                  setShowPasswordCheck(true);\r\n                                  setIsFocused(false);\r\n                                  setAuthError(null);\r\n                                }}\r\n                                onBlur={(e) => {\r\n                                  field.onBlur(e);\r\n                                  if (e.target.value.replace(/\\s/g, \"\").length === 0) {\r\n                                    setShowPasswordCheck(false);\r\n                                    setIsFocused(false);\r\n                                  }\r\n                                }}\r\n                                onFocus={() => {\r\n                                  setShowPasswordCheck(true);\r\n                                }}\r\n                                error={\r\n                                  submitCount > 0 && meta.error ? <InputError message={meta.error} /> : null\r\n                                }\r\n                                isError={submitCount > 0 && meta.error ? true : false}\r\n                              />\r\n                              {showPasswordCheck && (\r\n                                <PasswordValidation\r\n                                  password={password}\r\n                                  isValid={touched.password && !errors.password}\r\n                                />\r\n                              )}\r\n                            </>\r\n                          )}\r\n                        </Field>\r\n                      </div>\r\n\r\n                      <div className=\"authCorrectIcon\">\r\n                        <div className=\"checkIcon\">\r\n                          {values.password_confirmation && !errors.password_confirmation && dirty && <CheckIcon width=\"25\" height=\"25\" />}\r\n                        </div>\r\n                        <Field name=\"password_confirmation\">\r\n                          {({ field, meta }) => (\r\n                            <TextInput\r\n                              {...field}\r\n                              placeholder=\"Confirm Password\"\r\n                              maxLength={64}\r\n                              type=\"password\"\r\n                              error={values.password_confirmation && errors.password_confirmation && dirty ? <InputError message={meta.error} /> : null}\r\n                              isError={values.password_confirmation && errors.password_confirmation && dirty ? true : false}\r\n                              onChange={(e) => {\r\n                                field.onChange(e);\r\n                                setAuthError(null);\r\n                              }}\r\n                            />\r\n                          )}\r\n                        </Field>\r\n                      </div>\r\n\r\n                      <div className=\"w-100\">\r\n                        <CommonButton\r\n                          type=\"submit\"\r\n                          title={isSubmitting ? 'Loading' : 'Create Account'}\r\n                          fluid\r\n                          disabled={!(dirty && isValid) || isSubmitting}\r\n                        />\r\n                      </div>\r\n\r\n                      <div className=\"anAccount mt-3 text-center\">\r\n                        <h6>\r\n                          Already have an account?\r\n                          <NavLink href=\"/login\" className=\"ml-1\">Login</NavLink>\r\n                        </h6>\r\n                      </div>\r\n\r\n                      {authError && (\r\n                        <div className=\"d-flex justify-content-center\">\r\n                          <div className=\"signup_invalid_credential mt-3\">\r\n                            <div className=\"d-flex gap-1 align-items-center mb-1\">\r\n                              <RedErrorCircle />\r\n                              <p className=\"ml-2\">{authError}</p>\r\n                            </div>\r\n\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </Form>\r\n                  )}\r\n                </Formik>\r\n\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-4 mt-md-5\">\r\n            <LoginFooter />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </AuthLayout>\r\n  );\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;;;;;;;;;;;;;AAwBA,MAAM,gBAAgB;IACpB,OAAO;IACP,UAAU;IACV,uBAAuB;AACzB;AAEe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,OAAO,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD,KAAK,+BAA+B;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAC1B,MAAM,YAAY,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;YAEpD,IAAI,CAAC,aAAa,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE;gBAC/C,eAAe,UAAU,CAAC;gBAC1B,OAAO,IAAI,CAAC;YACd;QACF;6BAAG,EAAE;IAEL,MAAM,eAAe,OAAO,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE;QAC1D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;QAC5B,MAAM,mBAAmB;QACzB,MAAM,YAAY,KAAK,GAAG,KAAK,mBAAmB,KAAK;QAEvD,MAAM,kBAAkB;YAAC;YAAQ;YAAO;YAAW;SAAY;QAE/D,MAAM,UAAU,gBAAgB,QAAQ,CAAC,eAAe,OAAO,CAAC,cAC5D,eAAe,OAAO,CAAC,aACvB;QAEJ,MAAM,QAAQ,eAAe,OAAO,CAAC,aAAa;QAElD,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,WAAQ,AAAD,EAAE;gBAAE;gBAAO;gBAAU;gBAAM;gBAAS;YAAM;YAExE,IAAI,CAAC,SAAS,OAAO,EAAE;gBACrB,aAAa,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;gBACvC;YACF;YACA,aAAa;YACb,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAC1B,eAAe,OAAO,CAAC,gBAAgB,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD,EAAE,OAAO,KAAK;YAC7D,eAAe,OAAO,CACpB,eACA,KAAK,SAAS,CAAC;gBAAE;gBAAM;YAAU;YAGnC,OAAO,IAAI,CAAC,CAAC,kCAAkC,CAAC;QAClD,EAAE,OAAO,OAAO;YACd;YACA,aAAa;QACf;IACF;IAGA,MAAM,YAAY;QAChB,SAAS;QACT,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,eAAe;QACf,qBAAqB;IACvB;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAU;;0BACT,6LAAC,iIAAA,CAAA,UAAQ;gBAAC,OAAO;;;;;;0BACjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM;;kDACV,6LAAC,uIAAA,CAAA,oBAAiB;wCAAC,OAAM;;;;;;oCAAuB;;;;;;;;;;;;sCAGpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0IAAA,CAAA,UAAQ;;;;;8CACT,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;kDAAG;;;;;;;;;;;8CAEN,6LAAC,kJAAA,CAAA,UAAe;oCAAC,MAAK;;;;;;8CACtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;kDAAK;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kJAAA,CAAA,SAAM;4CACL,eAAe;4CACf,kBAAkB,+HAAA,CAAA,eAAY;4CAC9B,UAAU;sDAET,CAAC,EACA,YAAY,EACZ,MAAM,EACN,MAAM,EACN,OAAO,EACP,YAAY,EACZ,SAAS,EACT,KAAK,EACL,OAAO,EACP,WAAW,EACZ,iBACC,6LAAC,kJAAA,CAAA,OAAI;;sEACH,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK,IAAI,uBAAS,6LAAC,uIAAA,CAAA,YAAS;wEAAC,OAAM;wEAAK,QAAO;;;;;;;;;;;8EAE1E,6LAAC,kJAAA,CAAA,QAAK;oEAAC,MAAK;8EACT,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,iBACf,6LAAC,wIAAA,CAAA,UAAS;4EACP,GAAG,KAAK;4EACT,aAAY;4EACZ,MAAK;4EACL,WAAW;4EACX,OAAO,KAAK,OAAO,IAAI,KAAK,KAAK,iBAAG,6LAAC,yIAAA,CAAA,UAAU;gFAAC,SAAS,KAAK,KAAK;;;;;yFAAO;4EAC1E,SAAS,KAAK,OAAO,IAAI,KAAK,KAAK,GAAG,OAAO;4EAC7C,UAAU,CAAC;gFACT,MAAM,QAAQ,CAAC;gFACf,aAAa;4EACf;;;;;;;;;;;;;;;;;sEAMR,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,IAAI,uBAAS,6LAAC,uIAAA,CAAA,YAAS;wEAAC,OAAM;wEAAK,QAAO;;;;;;;;;;;8EAEhF,6LAAC,kJAAA,CAAA,QAAK;oEAAC,MAAK;8EACT,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,iBAC1C;;8FACE,6LAAC,wIAAA,CAAA,UAAS;oFACP,GAAG,KAAK;oFACT,aAAY;oFACZ,MAAK;oFACL,WAAW;oFACX,UAAU,CAAC;wFACT,MAAM,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;wFACnD,MAAM,QAAQ,CAAC;4FAAE,QAAQ;gGAAE,MAAM,EAAE,MAAM,CAAC,IAAI;gGAAE,OAAO;4FAAa;wFAAE;wFACtE,YAAY;wFACZ,qBAAqB;wFACrB,aAAa;wFACb,aAAa;oFACf;oFACA,QAAQ,CAAC;wFACP,MAAM,MAAM,CAAC;wFACb,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;4FAClD,qBAAqB;4FACrB,aAAa;wFACf;oFACF;oFACA,SAAS;wFACP,qBAAqB;oFACvB;oFACA,OACE,cAAc,KAAK,KAAK,KAAK,iBAAG,6LAAC,yIAAA,CAAA,UAAU;wFAAC,SAAS,KAAK,KAAK;;;;;iGAAO;oFAExE,SAAS,cAAc,KAAK,KAAK,KAAK,GAAG,OAAO;;;;;;gFAEjD,mCACC,6LAAC,4JAAA,CAAA,UAAkB;oFACjB,UAAU;oFACV,SAAS,QAAQ,QAAQ,IAAI,CAAC,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;sEAQzD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,OAAO,qBAAqB,IAAI,CAAC,OAAO,qBAAqB,IAAI,uBAAS,6LAAC,uIAAA,CAAA,YAAS;wEAAC,OAAM;wEAAK,QAAO;;;;;;;;;;;8EAE1G,6LAAC,kJAAA,CAAA,QAAK;oEAAC,MAAK;8EACT,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,iBACf,6LAAC,wIAAA,CAAA,UAAS;4EACP,GAAG,KAAK;4EACT,aAAY;4EACZ,WAAW;4EACX,MAAK;4EACL,OAAO,OAAO,qBAAqB,IAAI,OAAO,qBAAqB,IAAI,sBAAQ,6LAAC,yIAAA,CAAA,UAAU;gFAAC,SAAS,KAAK,KAAK;;;;;yFAAO;4EACrH,SAAS,OAAO,qBAAqB,IAAI,OAAO,qBAAqB,IAAI,QAAQ,OAAO;4EACxF,UAAU,CAAC;gFACT,MAAM,QAAQ,CAAC;gFACf,aAAa;4EACf;;;;;;;;;;;;;;;;;sEAMR,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,2IAAA,CAAA,UAAY;gEACX,MAAK;gEACL,OAAO,eAAe,YAAY;gEAClC,KAAK;gEACL,UAAU,CAAC,CAAC,SAAS,OAAO,KAAK;;;;;;;;;;;sEAIrC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;;oEAAG;kFAEF,6LAAC,sIAAA,CAAA,UAAO;wEAAC,MAAK;wEAAS,WAAU;kFAAO;;;;;;;;;;;;;;;;;wDAI3C,2BACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uIAAA,CAAA,iBAAc;;;;;sFACf,6LAAC;4EAAE,WAAU;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAazC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0IAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxB;GAhPwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 4418, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}