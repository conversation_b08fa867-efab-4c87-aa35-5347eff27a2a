/* [project]/src/css/Home/Testimonials.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.testimonials_sec {
  padding-bottom: 150px;
  padding-top: 85px;
  position: relative;
  z-index: 1;
}

.testimonials_sec:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 60%;
  border-radius: 2rem;
  z-index: -1;
}

@media (width <= 1199px) {
  .testimonials_sec {
    padding-bottom: 80px;
  }
}

@media (width <= 767px) {
  .testimonials_sec {
    padding-bottom: 70px;
    background-color: #001331;
    padding-top: 50px;
  }

  .testimonials_sec_content {
    padding-bottom: 30px;
  }
}

.testimonials_sec_content h3 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 20px;
}

@media (width <= 1199px) {
  .testimonials_sec_content h3 {
    font-size: 2.5rem;
  }
}

@media (width <= 767px) {
  .testimonials_sec_content h3 {
    font-size: 2rem;
  }
}

.testimonials_sec_content p {
  font-size: 22px;
  font-weight: 600;
}

@media (width <= 767px) {
  .testimonials_sec_content p {
    font-size: 18px;
  }
}

.testimonials_sec .slider-container {
  max-width: 850px;
  margin-left: auto;
}

.testimonials_sec .slider-container .slick-slider .slick-list .slick-track .slick-slide {
  padding-left: 60px;
  padding-top: 100px;
}

@media (width <= 991px) {
  .testimonials_sec .slider-container .slick-slider .slick-list .slick-track .slick-slide {
    padding-top: 0;
    padding-left: 20px;
  }
}

@media (width <= 767px) {
  .testimonials_sec .slider-container .slick-slider .slick-list .slick-track .slick-slide {
    padding-left: 10px;
  }
}

@media (width <= 575px) {
  .testimonials_sec .slider-container .slick-slider .slick-list .slick-track .slick-slide {
    padding-left: 0;
  }
}

.testimonials_sec .slider-container .slick-slider .slick-list .slick-track .slick-slide.slick-active {
  padding-top: 0;
}

.testimonials_sec .slider-container .slick-slider .slick-list .slick-track .slick-slide.slick-active.slick-current {
  padding-top: 100px;
}

@media (width <= 991px) {
  .testimonials_sec .slider-container .slick-slider .slick-list .slick-track .slick-slide.slick-active.slick-current {
    padding-top: 0;
  }
}

.testimonials_sec .slider-container .slick-slider .slick-arrow.slick-prev, .testimonials_sec .slider-container .slick-slider .slick-arrow.slick-next {
  position: absolute;
  bottom: -30px;
  left: auto;
  right: 0;
  top: auto;
  width: 50px;
  height: 50px;
  border-radius: 10rem;
  background-color: #00adef;
  z-index: 2;
}

@media (width <= 991px) {
  .testimonials_sec .slider-container .slick-slider .slick-arrow.slick-prev, .testimonials_sec .slider-container .slick-slider .slick-arrow.slick-next {
    bottom: -99px;
  }
}

@media (width <= 767px) {
  .testimonials_sec .slider-container .slick-slider .slick-arrow.slick-prev, .testimonials_sec .slider-container .slick-slider .slick-arrow.slick-next {
    width: 40px;
    height: 40px;
    bottom: -70px;
    top: auto;
  }
}

.testimonials_sec .slider-container .slick-slider .slick-arrow.slick-prev {
  right: 70px;
}

@media (width <= 767px) {
  .testimonials_sec .slider-container .slick-slider .slick-arrow.slick-prev {
    right: 60px;
  }
}

@media (width >= 1200px) {
  .testimonials_sec .bottom-btn-0 {
    bottom: -3px;
    left: auto;
    position: absolute !important;
    right: 0 !important;
  }
}

@media (width <= 1200px) {
  .testimonials_sec .bottom-btn-0 {
    display: flex;
  }
}

.testimonials_sec .testimonial_card {
  background-color: #fff;
  padding: 3rem;
  justify-content: space-between;
  flex-direction: column;
  min-height: 450px;
  display: flex !important;
}

@media (width >= 991px) {
  .testimonials_sec .testimonial_card {
    max-width: 380px;
  }
}

@media (width <= 991px) {
  .testimonials_sec .testimonial_card {
    max-width: 100%;
    padding: 2rem 1.5rem;
  }
}

.testimonials_sec .testimonial_card p {
  color: #000;
  font-size: 20px;
  padding: 2.5rem 0;
  margin: 0 auto;
}

.testimonials_sec .testimonial_card h3 {
  color: #00adef;
  font-size: 1.5rem;
  margin-bottom: 0;
  text-align: right;
  position: relative;
}

@media (width <= 991px) {
  .testimonials_sec .testimonial_card h3 {
    padding-right: 2rem;
  }
}

.testimonials_sec .testimonial_card h3:after {
  content: "";
  position: absolute;
  bottom: 0;
  right: -3rem;
  width: 40px;
  height: 30px;
  background-color: #00adef;
}

@media (width <= 991px) {
  .testimonials_sec .testimonial_card h3:after {
    right: -24px;
    width: 30px;
  }
}

/*# sourceMappingURL=src_css_Home_Testimonials_scss_b52d8e88.css.map*/