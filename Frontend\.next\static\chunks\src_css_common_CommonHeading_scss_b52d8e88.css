/* [project]/src/css/common/CommonHeading.scss.css [app-client] (css) */
:root {
  --font-g<PERSON>roy: "<PERSON><PERSON>", sans-serif;
}

.common_heading h2, .common_heading h1 {
  font-size: 5rem;
  line-height: normal;
  color: #fff;
  font-weight: 800;
  font-family: <PERSON><PERSON>, sans-serif;
}

@media (width <= 1269px) {
  .common_heading h2, .common_heading h1 {
    font-size: 3rem !important;
  }
}

@media (width <= 991px) {
  .common_heading h2, .common_heading h1 {
    font-size: 3rem !important;
  }
}

@media (width <= 767px) {
  .common_heading h2, .common_heading h1 {
    font-size: 2.5rem !important;
  }
}

/*# sourceMappingURL=src_css_common_CommonHeading_scss_b52d8e88.css.map*/