/* [project]/src/css/Home/OurMission.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.our_mission {
  padding: 170px 0 205px;
  position: relative;
  z-index: 1;
  background: linear-gradient(#000 74.64%, #001331 90%);
  mix-blend-mode: screen;
}

@media (width <= 767px) {
  .our_mission {
    padding: 50px 0;
  }

  .our_mission:before {
    content: "";
    position: absolute;
    top: 0%;
    left: 0;
    width: 100%;
    height: 75%;
    background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-maximize-profits.png");
    background-position: top;
    background-repeat: no-repeat;
    z-index: 0;
  }
}

.our_mission_content {
  max-width: 900px;
  margin: 0 auto;
}

.our_mission_content p {
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  letter-spacing: -1px;
  text-align: left;
  padding-top: 2rem;
  width: 100%;
}

@media (width <= 767px) {
  .our_mission_content p {
    font-size: 20px;
    line-height: 30px;
  }
}

@media (width <= 575px) {
  .our_mission_content p {
    font-size: 18px;
    line-height: 28px;
  }
}

/*# sourceMappingURL=src_css_Home_OurMission_scss_b52d8e88.css.map*/