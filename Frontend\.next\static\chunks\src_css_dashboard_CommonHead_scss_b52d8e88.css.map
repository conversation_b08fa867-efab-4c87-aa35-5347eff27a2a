{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/dashboard/CommonHead.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.common_head_container{position:sticky;top:80px;bottom:50px;z-index:999}@media(width <= 1199px){.common_head_container{top:77.3px}}@media(width <= 767px){.common_head_container{top:64.16px}}@media(width <= 360px){.common_head_container{top:64.16px}}.common_head{background-color:#031940;padding:1.25rem;border-radius:1.25rem;border-bottom:1px solid #064197}@media(max-width: 1199px){.common_head{padding:15}}@media(max-width: 767px){.common_head{border:1px solid #064197}}@media(max-width: 550px){.common_head_md{display:none}}.common_head_small{display:none}.common_head_small button{min-width:30px;min-height:30px;border-radius:50%;display:flex;justify-content:center;align-items:center;background-color:#00ade<PERSON>}@media(max-width: 550px){.common_head_small button{min-width:25px;min-height:25px}.common_head_small button svg{height:15px !important;width:15px !important}}.common_head_small button svg{height:20px;width:20px}.common_head_small .layout_fix{display:flex;justify-content:space-between}@media(max-width: 550px){.common_head_small{display:block}}.common_head .commom_tradeacct{display:flex;flex-wrap:wrap;width:100%}.common_head .commom_tradeacct .account{background-color:#04498c;padding:8px 15px;height:100%;width:100%;border-radius:10px 10px 0 0}.common_head .commom_tradeacct .account h6{color:#fff;font-size:1rem;line-height:1}@media(max-width: 1199px){.common_head .commom_tradeacct .account h6{font-size:14px}}.common_head .commom_tradeacct .number,.common_head .commom_tradeacct select{background-color:#fff;padding:5px 15px;height:100%;width:100%;border-radius:0 0 10px 10px;font-size:1rem;font-weight:600}@media(max-width: 1199px){.common_head .commom_tradeacct .number,.common_head .commom_tradeacct select{font-size:14px}}.common_head .commom_tradeacct .form-select:focus{border-color:rgba(0,0,0,0) !important;box-shadow:none !important}.common_head_search .commonSearch .form-control{width:100%;border:0}@media(max-width: 1199px){.common_head_search .commonSearch .form-control{min-height:66px}}@media(max-width: 767px){.common_head_search .commonSearch .form-control{min-height:60px}}.common_head_datebox{background-color:hsla(0,0%,100%,.3);min-height:70px;border-radius:1rem;padding:8px 15px}@media(max-width: 1199px){.common_head_datebox{padding:8px 10px;min-height:66px}}@media(max-width: 767px){.common_head_datebox{min-height:auto}}.common_head_datebox h6{color:hsla(0,0%,100%,.6);padding-top:5px}@media(max-width: 1199px){.common_head_datebox h6{font-size:14px}.common_head_datebox h5{font-size:16px}}.head_draft{margin-top:10px}.head_draft_icons{display:flex;align-items:center;gap:5px}.head_draft_icons p{font-weight:600}.head_draft_icons svg,.head_draft_icons img{width:20px;height:20px}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;AAAwE;EAAwB;;;;;AAAmC;EAAuB;;;;;AAAoC;EAAuB;;;;;AAAoC;;;;;;;AAA4G;EAA0B;;;;;AAAyB;EAAyB;;;;;AAAuC;EAAyB;;;;;AAA8B;;;;AAAgC;;;;;;;;;;AAA2J;EAAyB;;;;;EAAyD;;;;;;AAA4E;;;;;AAAqD;;;;;AAA0E;EAAyB;;;;;AAAkC;;;;;;AAAsE;;;;;;;;AAAqI;;;;;;AAAmF;EAA0B;;;;;AAA2D;;;;;;;;;;AAAsM;EAA0B;;;;;AAA6F;;;;;AAAmH;;;;;AAAoE;EAA0B;;;;;AAAiE;EAAyB;;;;;AAAiE;;;;;;;AAA6G;EAA0B;;;;;;AAAuD;EAAyB;;;;;AAAsC;;;;;AAAiE;EAA0B;;;;EAAuC;;;;;AAAwC;;;;AAA4B;;;;;;AAA0D;;;;AAAoC"}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}