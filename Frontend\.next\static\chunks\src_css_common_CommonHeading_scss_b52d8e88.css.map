{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/common/CommonHeading.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.common_heading h2,.common_heading h1{font-size:5rem;line-height:normal;color:#fff;font-weight:800;font-family:\"<PERSON><PERSON>\",sans-serif}@media(max-width: 1269px){.common_heading h2,.common_heading h1{font-size:3rem !important}}@media(max-width: 991px){.common_heading h2,.common_heading h1{font-size:3rem !important}}@media(max-width: 767px){.common_heading h2,.common_heading h1{font-size:2.5rem !important}}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;;AAAmI;EAA0B;;;;;AAAiE;EAAyB;;;;;AAAiE;EAAyB"}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}