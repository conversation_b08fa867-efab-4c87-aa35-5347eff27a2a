/* [project]/src/css/common/LoadingSpinnter.scss.css [app-client] (css) */
.customLoader {
  width: 48px;
  height: 48px;
  border: 5px solid #fff;
  border-bottom-color: #0000;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: 1s linear infinite rotation;
}

@keyframes rotation {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/*# sourceMappingURL=src_css_common_LoadingSpinnter_scss_b52d8e88.css.map*/