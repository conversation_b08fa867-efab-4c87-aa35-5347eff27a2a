{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_570db516._.js", "server/edge/chunks/[root of the server]__a8b25e96._.js", "server/edge/chunks/edge-wrapper_d6f08048.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/user/:path*{(\\\\.json)}?", "originalSource": "/user/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/pricing{(\\\\.json)}?", "originalSource": "/pricing"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/change-password{(\\\\.json)}?", "originalSource": "/change-password"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/account/:path*{(\\\\.json)}?", "originalSource": "/account/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/super-admin/:path*{(\\\\.json)}?", "originalSource": "/super-admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/not-found{(\\\\.json)}?", "originalSource": "/not-found"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/security-check{(\\\\.json)}?", "originalSource": "/security-check"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JAdWpMZW7YX2HWGBSRb2tC6MF8pA5fg8t+vFkOc3uBA=", "__NEXT_PREVIEW_MODE_ID": "0747596eb70923ebeee202639b2d9b06", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9e866c63ff1886778753c3814aeabc9450b3bfd1794f0eec020b1c9f6115403e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "aac87bb9356224ad3722193f8a44a311a45dab48632584448dbc5d3bce9f220f"}}}, "instrumentation": null, "functions": {}}