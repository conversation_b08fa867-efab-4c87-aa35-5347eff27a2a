{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/common/CustomPagination.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.customPagination .pagination .page-item .page-link{background-color:rgba(0,0,0,0);border:0;color:#00adef;width:30px;height:30px;display:flex;align-items:center;justify-content:center;padding:0;border:1px solid rgba(0,0,0,0);border-radius:5px;cursor:pointer;font-size:1.25rem}.customPagination .pagination .page-item .page-link:focus{box-shadow:none}.customPagination .pagination .page-item:last-child .page-link,.customPagination .pagination .page-item:first-child .page-link{border:0;background-color:#00adef;border-radius:10rem;width:30px;height:30px;display:flex;align-items:center;justify-content:center}.customPagination .pagination .page-item:last-child .page-link span,.customPagination .pagination .page-item:first-child .page-link span{display:flex}.customPagination .pagination .page-item:last-child{margin-left:1rem}.customPagination .pagination .page-item:first-child{margin-right:1rem}.customPagination .pagination .page-item.active .page-link{text-decoration:underline}.customPagination .pagination .page-item:hover .page-link{color:#0099d1;text-decoration:underline}.customPagination .pagination .page-item:hover:last-child .page-link,.customPagination .pagination .page-item:hover:first-child .page-link{background-color:#0099d1}.customPagination .pagination .page-item.disabled,.customPagination .pagination .page-item:disabled{border-radius:10rem;background-color:#414c60;pointer-events:none}.customPagination .pagination .page-item.disabled .page-link,.customPagination .pagination .page-item:disabled .page-link{background-color:#414c60;opacity:.6;cursor:not-allowed}.customPagination .pagination .prevArrow{transform:rotate(-180deg)}.paginate-arrows{background-color:#00adef;color:#fff;border:none;padding:0;cursor:pointer;font-size:1.2rem;min-width:30px;min-height:30px;display:flex;align-items:center;justify-content:center;border-radius:10rem;position:relative}.pagination a,.pagination .link{color:#00adef !important;cursor:pointer !important}.pagination .active{text-decoration:underline}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;;;;;;;;;;AAAoR;;;;AAA0E;;;;;;;;;;;AAAmQ;;;;AAAsJ;;;;AAAqE;;;;AAAuE;;;;AAAqF;;;;;AAAkG;;;;AAAoK;;;;;;AAAqK;;;;;;AAAiL;;;;AAAmE;;;;;;;;;;;;;;;;AAAuO;;;;;AAAmF"}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}