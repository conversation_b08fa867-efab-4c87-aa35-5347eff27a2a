{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/Home/HowitWorks.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.howit_works{padding:100px 0;background-color:#001331;position:relative;z-index:1}@media screen and (max-width: 767px){.howit_works{padding:50px 0}}.howit_works .icons_big_tabs{margin-top:70px}@media screen and (max-width: 767px){.howit_works .icons_big_tabs{margin-top:2rem}}.howit_works .icons_big_tabs .tab-content{margin-top:130px}@media screen and (max-width: 991px){.howit_works .icons_big_tabs .tab-content{margin-top:3rem}}.howit_works .icons_big_tabs .howit_content{max-width:850px;margin:0 auto}.howit_works .icons_big_tabs .howit_content figure img{max-width:210px}@media screen and (max-width: 991px){.howit_works .icons_big_tabs .howit_content figure img{max-width:150px}}@media screen and (max-width: 767px){.howit_works .icons_big_tabs .howit_content figure img{max-width:100px;margin:0 auto 10px;display:table}}.howit_works .icons_big_tabs .howit_content_text{padding-left:50px}@media screen and (max-width: 767px){.howit_works .icons_big_tabs .howit_content_text{padding-left:0;text-align:center}}@media screen and (max-width: 991px){.howit_works .icons_big_tabs .howit_content_text h3{font-size:36px;font-weight:800;line-height:45px}}.howit_works .icons_big_tabs .howit_content_text p{font-size:24px;font-weight:600;line-height:36px;margin:1.25rem 0}@media screen and (max-width: 767px){.howit_works .icons_big_tabs .howit_content_text p{font-size:18px;line-height:26px}}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;AAAkF;EAAqC;;;;;AAA6B;;;;AAA6C;EAAqC;;;;;AAA8C;;;;AAA2D;EAAqC;;;;;AAA2D;;;;;AAA0E;;;;AAAuE;EAAqC;;;;;AAAwE;EAAqC;;;;;;;AAAyG;;;;AAAmE;EAAqC;;;;;;AAAmF;EAAqC;;;;;;;AAAqG;;;;;;;AAAoH;EAAqC"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}