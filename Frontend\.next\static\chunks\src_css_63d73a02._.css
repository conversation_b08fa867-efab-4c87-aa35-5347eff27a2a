/* [project]/src/css/common/CommonButton.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.btn-style, .btn-primary {
  min-height: 66px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 10rem;
  padding: .5rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  background-color: #00adef;
  border: 0;
  text-transform: capitalize;
  transition: all .3s ease-in-out;
  min-width: 150px;
  color: #fff;
}

.btn-style span, .btn-primary span {
  line-height: 1;
}

@media (width <= 1599px) {
  .btn-style, .btn-primary {
    min-height: 66px;
  }
}

@media (width <= 1199px) {
  .btn-style, .btn-primary {
    min-height: 56px;
    font-size: 1.125rem;
    font-weight: 500;
  }
}

@media (width <= 767px) {
  .btn-style, .btn-primary {
    min-height: 46px;
    font-size: 1rem;
  }
}

.btn-style:hover, .btn-primary:hover {
  background-color: #0099d1;
  color: #fff;
}

.btn-style.transparent, .btn-primary.transparent {
  background-color: #0000;
  border: none;
}

.btn-style.white-btn, .btn-primary.white-btn {
  background: #fff;
  color: #000;
}

.btn-style.white-btn:hover, .btn-primary.white-btn:hover {
  background: #00adef;
  color: #fff;
}

.btn-style.yellow-btn, .btn-primary.yellow-btn {
  background-color: #fea500;
  color: #fff;
}

.btn-style.yellow-btn:hover, .btn-primary.yellow-btn:hover {
  background-color: #c9870d;
  color: #fff;
}

.btn-style.gray-btn, .btn-primary.gray-btn {
  color: #fff;
  background-color: #5e6165 !important;
}

.btn-style.gray-btn:hover, .btn-primary.gray-btn:hover {
  background-color: #708090;
  color: #fff;
}

.btn-style.gradient-btn, .btn-primary.gradient-btn {
  background: linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);
  color: #fff;
}

.btn-style.gradient-btn:hover, .btn-primary.gradient-btn:hover {
  background: linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);
  color: #fff;
}

.btn-style.green-btn, .btn-primary.green-btn {
  background-color: #32cd33;
  color: #fff;
}

.btn-style.green-btn:hover, .btn-primary.green-btn:hover {
  background-color: #2bb72b;
  color: #fff;
}

.btn-style.red-btn, .btn-primary.red-btn {
  background-color: #ff696a;
  color: #fff;
}

.btn-style.red-btn:hover, .btn-primary.red-btn:hover {
  background-color: #e65f60;
  color: #fff;
}

.btn-style.border-btn, .btn-primary.border-btn {
  background: none;
  color: #fff;
  border: 1px solid #00adef;
}

.btn-style.border-btn:hover, .btn-primary.border-btn:hover {
  background: #00adef;
  color: #fff;
}

.btn-style .onlyIcon, .btn-primary .onlyIcon {
  margin-right: 15px;
  display: inline-flex;
}

.btn-style:disabled, .btn-style.disabled, .btn-primary:disabled, .btn-primary.disabled {
  background: #c5c5d5;
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
}

:disabled, .disabled {
  background-color: #414c60;
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
}

.white20 {
  background-color: #ffffff1f;
  width: 100%;
}


/* [project]/src/css/common/Header.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

header {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 9998;
}

.home-page .siteHeader {
  border-bottom: 0;
  background-color: #000 !important;
}

@media (width >= 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.home-page .siteHeader .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media (width >= 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown .dropdown-menu {
    background-color: #1e222d !important;
  }
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background-color: #2a2e39 !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  font-weight: 700;
  color: #00b9ff;
  transition: none;
  background: linear-gradient(to right, #2a2e39, #1e222d) !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #2a2e39 !important;
}

@media (width <= 1199px) {
  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    font-weight: 700;
    color: #00aeef;
    transition: none;
    background: linear-gradient(to right, #000, #2d2d2d) !important;
  }

  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .home-page .siteHeader .navbar-collapse {
    background-color: #000000e6 !important;
  }
}

@media (width >= 1200px) {
  .home-page .siteHeader .navbar-collapse .nav-link:hover, .home-page .siteHeader .navbar-collapse .nav-link.active, .home-page .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .languageDropdown {
    width: 64px;
  }
}

@media (width >= 1200px) and (width <= 1199px) {
  .home-page .languageDropdown {
    width: 100%;
  }

  .home-page .languageDropdown .common_dropdown {
    width: 100%;
  }
}

@media (width >= 1200px) {
  .home-page .languageDropdown .common_dropdown .nav-link:hover, .home-page .languageDropdown .common_dropdown .nav-link.active, .home-page .languageDropdown .common_dropdown .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
    background: #2a2e39 !important;
  }
}

.siteHeader {
  height: 80px;
  padding: 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  background-color: #031940;
  border-bottom: 1px solid #064197;
}

.siteHeader .btn-style {
  min-height: 56px;
  min-width: 169px;
}

@media (width <= 1199px) {
  .siteHeader .btn-style {
    min-height: 40px;
    min-width: 120px;
    padding: 8px 1rem;
    font-size: 14px;
  }
}

@media (width <= 575px) {
  .siteHeader .btn-style {
    min-height: 34px;
    min-width: 80px;
    font-size: 14px;
  }
}

@media (width <= 1199px) {
  .siteHeader {
    z-index: 9999;
    backdrop-filter: none;
  }
}

@media (width <= 767px) {
  .siteHeader {
    padding: .625rem 0;
  }
}

.siteHeader .navbar {
  padding: 0;
  width: 100%;
}

.siteHeader .navbar .brandLogo img {
  max-width: 190px;
  width: 100%;
}

@media (width <= 767px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 150px;
    margin-right: 0;
  }
}

@media (width <= 360px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 120px;
    margin-right: 0;
  }
}

.siteHeader .navbar-collapse {
  height: auto !important;
}

.siteHeader .navbar-collapse .nav-link {
  font-size: 1.25rem;
  font-weight: 400;
  background-color: #0000;
  display: flex;
  align-items: center;
  white-space: nowrap;
  padding: .5rem 1.5rem;
  color: #fff;
}

.siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
  color: #00adef;
}

@media (width >= 1200px) {
  .siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #283f67 !important;
  }

  .siteHeader .navbar-collapse .nav-link {
    margin: 0 3px;
  }
}

@media (width <= 1199px) {
  .siteHeader .navbar-collapse .nav-link {
    padding: 1.25rem 0;
    border-bottom: 1px solid #fff3;
    font-size: 1.125rem;
  }

  .siteHeader .navbar-collapse .nav-link img {
    width: 22px;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    font-weight: 700;
    background: linear-gradient(to right, #031940, #283f67);
    color: #00aeef;
    transition: none;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .siteHeader .navbar-collapse {
    position: fixed;
    left: -350px;
    top: 0;
    background-color: #031940e6;
    backdrop-filter: blur(5px);
    width: 350px;
    padding: 1.25rem 1rem;
    display: block;
    transition: all .2s ease-in-out;
    z-index: 9999;
    padding: 0;
    height: 100vh !important;
  }

  .siteHeader .navbar-collapse a {
    display: flex;
    justify-content: flex-start;
    text-align: left;
  }

  .siteHeader .navbar-collapse.show {
    left: 0;
    height: 100vh;
  }

  .siteHeader .navbar-collapse .navMenu {
    padding: 20px;
    height: calc(100vh - 90px);
    overflow-y: auto;
  }
}

@media (width <= 767px) {
  .siteHeader .navbar-collapse {
    left: -100%;
    width: 100%;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
  border-radius: 0;
  padding: .5rem 1.5rem !important;
}

@media (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 1px solid #fff3;
    width: 100%;
    padding: 1.25rem 0 !important;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
  display: block;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg");
  background-repeat: no-repeat;
  background-size: 1.15rem;
  background-position: center;
  width: 1.15rem;
  height: 1.15rem;
  border: 0;
  transition: all .3s ease-in-out;
  margin-left: 1rem;
}

@media (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
    margin-left: 0;
    position: absolute;
    right: 0;
  }
}

@media (width >= 1200px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    background-color: #283f67;
    color: #fff;
  }

  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu {
    position: static;
    border: 0;
    background-color: #0000;
    padding: 0;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
  padding: .875rem 1.5rem;
  align-items: start;
  font-weight: 400 !important;
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
    padding: .875rem 1rem;
  }
}

.siteHeader .navbar .navbar-toggler {
  background-color: #0000;
  margin-left: 0;
  padding: 0;
  position: relative;
  width: 24px;
  height: 18px;
}

.siteHeader .navbar .navbar-toggler:focus {
  box-shadow: none;
}

@media (width <= 1199px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

@media (width <= 767px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

.siteHeader .navbar .navbar-toggler:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 24px;
  background-color: #fff;
  height: 2px;
  transition: all .3s ease-in-out;
}

.siteHeader .navbar .navbar-toggler:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  background-color: #fff;
  height: 2px;
  transition: all .3s ease-in-out;
}

.siteHeader .navbar .navbar-toggler .navbar-toggler-icon {
  background-image: none;
  height: 2px;
  background-color: #fff;
  width: 24px;
  transition: all .3s ease-in-out;
  display: flex;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  border-radius: .625rem;
  font-size: 1.25rem;
  padding: 0;
  display: flex;
  align-items: center;
  padding: .5rem .2rem !important;
}

@media (width <= 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1.125rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle:after {
  display: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle.show svg path {
  fill: #00adef;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
  border-radius: .625rem;
  border: 1px solid #ffffff4d;
  min-width: 200px;
  position: absolute;
  top: 45px;
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
    position: static;
    padding: 0;
    min-width: 100%;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
  font-size: 1.125rem;
  font-weight: 600;
  padding: .625rem 1rem;
  color: #fff;
}

@media (width <= 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
    font-size: 1rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item img {
  margin-right: 10px;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background: #283f67;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  font-weight: 700;
  background: linear-gradient(to right, #283f67, #031940);
  color: #00b9ff;
  transition: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #283f67 !important;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show img {
  width: 18px;
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar .openmenuSidebar {
    border-bottom: 1px solid #ffffff80;
    padding: 30px 15px;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo {
    padding: 0;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo img {
    max-width: 150px;
  }

  .siteHeader .navbar .openmenuSidebar .navbar-toggler {
    position: absolute;
    right: 15px;
  }
}

.siteHeader.openmenu .navbar .navbar-toggler:after {
  transform: rotate(45deg)translate(-5px, -5px);
  background-color: #fff;
}

.siteHeader.openmenu .navbar .navbar-toggler:before {
  transform: rotate(-45deg)translate(-5px, 5px);
  background-color: #fff;
}

.siteHeader.openmenu .navbar .navbar-toggler .navbar-toggler-icon {
  opacity: 0;
}

.siteHeader .user_icon img {
  width: 26px;
  height: 26px;
}

@media screen and (width <= 767px) {
  .siteHeader .sidebar_backdrop {
    display: none;
  }
}

.languageDropdown {
  width: 64px;
}

@media (width <= 1199px) {
  .languageDropdown {
    width: 100%;
  }

  .languageDropdown .common_dropdown {
    width: 100%;
  }
}

.languageDropdown .common_dropdown .nav-link:hover, .languageDropdown .common_dropdown .nav-link.active, .languageDropdown .common_dropdown .nav-link:focus {
  color: #fff;
  background-color: #283f67 !important;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  font-size: 1.25rem;
  padding: 0;
  display: flex;
  align-items: center;
  border-radius: 0 !important;
}

@media (width <= 991px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1rem;
  }
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle svg {
  margin-right: 10px;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle:focus, .languageDropdown .common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: #0000 !important;
}

@media (width <= 1199px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    width: 100%;
  }
}

.languageDropdown .globalIcon .icon {
  transition: opacity .3s;
}

.languageDropdown .globalIcon .blue {
  display: none;
}

.languageDropdown .nav-item:hover .globalIcon .black, .languageDropdown .nav-item.show .globalIcon .black {
  display: none;
}

.languageDropdown .nav-item:hover .globalIcon .blue, .languageDropdown .nav-item.show .globalIcon .blue {
  display: block;
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
  display: none;
}

@media screen and (width <= 1199px) {
  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
    display: block;
    padding-left: 10px;
    font-size: 18px;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name svg {
    width: 26px;
    height: 26px;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 0 !important;
  }
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: #0000 !important;
}

@media (width <= 1199px) {
  .brandLogo {
    display: flex;
  }

  .brandLogo img {
    max-width: 150px;
  }
}

@media (width <= 767px) {
  .brandLogo img {
    max-width: 110px;
  }
}

@media (width <= 359px) {
  .brandLogo img {
    max-width: 100px;
  }
}

.sidebar_backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1000;
  background-color: #0003;
  transition: all .2s ease-in-out;
}

.image_color_to_white {
  filter: brightness(0) invert();
}

@media (width >= 1200px) {
  .nav-link:hover, .nav-link.active, .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }
}


/* [project]/src/css/common/Footer.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.site_footer {
  background-color: #000;
}

.site_footer_inner {
  padding: 70px 0;
}

@media screen and (width <= 991px) {
  .site_footer_inner {
    padding: 40px 0;
  }
}

.site_footer_logo img {
  width: 200px;
}

.site_footer_content p {
  color: #ffffffa6;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
  letter-spacing: -.1px;
  margin-top: 20px;
}

@media screen and (width <= 991px) {
  .site_footer_content p {
    font-size: 16px;
  }
}

@media screen and (width <= 767px) {
  .site_footer_links {
    margin-top: 20px;
  }
}

.site_footer_links h4 {
  color: #c5c5d5;
  margin-bottom: 1.25rem;
  font-size: 1.65rem;
  line-height: 35px;
  font-weight: 600;
}

@media screen and (width <= 991px) {
  .site_footer_links h4 {
    font-size: 18px;
  }
}

.site_footer_links ul li a {
  font-size: 20px;
  font-weight: 600;
  line-height: 24.5px;
  letter-spacing: -.1px;
  color: #fff;
  transition: all .3s ease-in-out;
  padding-bottom: 10px;
}

@media screen and (width <= 991px) {
  .site_footer_links ul li a {
    font-size: 16px;
  }
}

.site_footer_links ul li a:hover, .site_footer_links ul li a.active {
  color: #00adef;
}

.site_footer_copyright {
  padding: 1.25rem 0;
  border-top: 1px solid #fff;
}

.site_footer_copyright p {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
  letter-spacing: -.1px;
}

@media screen and (width <= 991px) {
  .site_footer_copyright p {
    font-size: 16px;
  }
}


/* [project]/src/css/UserProfile.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.public_user_profile {
  padding: 2.813rem 0;
}

@media (width <= 550px) {
  .public_user_profile {
    padding: 1rem 0;
  }
}

.public_user_profile .sidebar_heading {
  width: 100%;
  margin-bottom: 26px;
}

.public_user_profile .sidebar_heading h2 {
  font-size: 48px;
  font-weight: 400;
  font-family: Gilroy-Bold, sans-serif;
  display: inline-flex;
}

@media (width <= 1199px) {
  .public_user_profile .sidebar_heading h2 {
    font-size: 1.688rem;
  }
}

@media (width <= 767px) {
  .public_user_profile .sidebar_heading h2 {
    font-size: 1.5rem;
  }
}

.public_user_profile .account_search {
  width: 100%;
  height: 72px;
  background-color: #fff3;
  padding: 10px 20px;
  border-radius: 15px;
  display: flex;
  gap: 16px;
  margin-bottom: 26px;
}

.public_user_profile .account_search input {
  background-color: #0000;
  width: 100%;
  height: 100%;
  color: #fff9;
  font-size: 20px;
  font-weight: 600;
}

.public_user_profile .account_search input:focus {
  box-shadow: none;
  outline: 0;
}

.public_user_profile .account_card_btnArrow {
  min-width: 30px;
  max-width: 30px;
  min-height: 29px;
  max-height: 29px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #00adef;
}

.public_user_profile .account_card_btnArrow svg {
  transition: all .3s ease-in-out;
  color: #fff !important;
  margin-left: 0 !important;
}

.public_user_profile .account_card_btnArrow svg path {
  fill: #fff !important;
}

@media (width <= 767px) {
  .public_user_profile .account_card_btnArrow svg {
    height: 10px !important;
  }
}

.public_user_profile .account_card_btnArrow:hover {
  background-color: #0099d1;
}

.public_user_profile .account_card_information {
  color: #000 !important;
}

.public_user_profile .account_card_information .main_inform {
  display: flex;
  gap: 20px;
}

@media (width <= 400px) {
  .public_user_profile .account_card_information .main_inform {
    display: block;
  }
}

.public_user_profile .account_card_information .main_inform h4 {
  font-size: 20px;
  font-weight: 600;
  color: #000 !important;
}

@media (width <= 991px) {
  .public_user_profile .account_card_information .main_inform h4 {
    font-size: 15px;
  }
}

.public_user_profile .account_card_information .main_inform .mail {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 7px;
  font-family: Gilroy-Semibold, sans-serif;
  color: #000 !important;
}

.public_user_profile .account_card_information .main_inform .location, .public_user_profile .account_card_information .main_inform .transaction, .public_user_profile .account_card_information .main_inform .active_sign, .public_user_profile .account_card_information .main_inform .since {
  font-size: 14px;
  font-family: Gilroy-Semibold, sans-serif;
  font-weight: 400;
  margin-bottom: 6px;
  display: flex;
  gap: 9px;
  color: #000 !important;
}

.public_user_profile .account_card_information .main_inform .active_sign span {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #d9d9d9;
  align-self: center;
}

.public_user_profile .account_card_information .main_inform .profile {
  display: flex;
  align-items: center;
  justify-content: center;
}

.public_user_profile .account_card_information .main_inform .profile .profile_photo {
  background-color: #0000001a;
  height: 100px;
  width: 100px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
}

.public_user_profile .account_card_information .main_inform .profile .profile_photo img {
  height: 100%;
  width: 100%;
  border-radius: 50%;
}

.public_user_profile .account_card_information .main_inform .round-bluefill-btn {
  background-color: #00adef;
  color: #fff;
  padding: 5px 15px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 15px;
  width: 100%;
  transition: all .3s ease-in-out;
  margin-top: 10px;
}

.public_user_profile .account_card_information .main_inform .round-bluefill-btn:hover {
  background-color: #0099d1;
}

.public_user_profile .account_card_about {
  color: #000 !important;
}

.public_user_profile .account_card_about .para_desc {
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 7px;
  line-height: 26px;
  color: #000 !important;
}

.public_user_profile .account_card_about .website-link {
  color: #00adef;
  font-size: 14px;
  font-weight: 700;
  word-break: break-all;
}

.public_user_profile .account_card_about .website-link:hover {
  color: #fea500;
}

.public_user_profile .account_card_about .table_form_textarea {
  background-color: #4f5e7a;
  border-radius: 6px;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  width: 100%;
  padding: 5px 8px;
}

.public_user_profile .account_card_about .table_form_textarea:focus {
  outline: none;
  background-color: #576887;
}

.public_user_profile .account_card_about .character-count {
  text-align: end;
  margin-top: 2px;
  color: #4f5e7a;
  font-size: 14px;
}

.public_user_profile .account_card_followers, .public_user_profile .account_card_following {
  color: #000 !important;
}

.public_user_profile .account_card_followers .main_inform, .public_user_profile .account_card_following .main_inform {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 6px;
}

.public_user_profile .account_card_followers h6, .public_user_profile .account_card_following h6 {
  color: #00adef;
  font-size: 16px;
  font-weight: 600;
}

.public_user_profile .account_card_followers .profile_photo, .public_user_profile .account_card_following .profile_photo {
  background-color: #0000001a;
  height: 50px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0 !important;
}

.public_user_profile .account_card_followers .profile_photo img, .public_user_profile .account_card_following .profile_photo img {
  height: 20px !important;
  width: 20px !important;
}

.public_user_profile .account_card_followers .unFollow_status, .public_user_profile .account_card_following .unFollow_status {
  background-color: #ff696a;
  height: 43px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  border-radius: 50px;
  flex-shrink: 0;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
}

.public_user_profile .account_card_followers .unFollow_status:hover, .public_user_profile .account_card_following .unFollow_status:hover {
  background-color: #fe5252;
}

.public_user_profile .account_card_marketplace {
  color: #000 !important;
}

.public_user_profile .account_card_marketplace .mini_card {
  border: 1px solid #********;
  padding: 10px;
  border-radius: 15px;
  margin-bottom: 10px;
}

.public_user_profile .account_card_marketplace .mini_card:last-child {
  margin-bottom: 0 !important;
}

.public_user_profile .account_card_marketplace .main_inform {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

@media (width <= 400px) {
  .public_user_profile .account_card_marketplace .main_inform {
    display: block;
  }
}

.public_user_profile .account_card_marketplace .star-rating {
  font-size: 14px;
  font-weight: 600;
}

.public_user_profile .account_card_marketplace .most_recent, .public_user_profile .account_card_marketplace .time {
  font-size: 14px;
  font-weight: 600;
  color: #000 !important;
}

.public_user_profile .account_card_marketplace .profile_photo {
  background-color: #0000001a;
  height: 50px;
  width: 50px;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
}

.public_user_profile .account_card_marketplace h6 {
  font-size: 18px;
  font-weight: 400;
  font-family: Gilroy-Semibold, sans-serif;
  color: #000 !important;
}

.public_user_profile .account_card_marketplace .mini_sc_title {
  display: none;
}

@media (width <= 767px) {
  .public_user_profile .account_card_marketplace h6 {
    display: none;
  }

  .public_user_profile .account_card_marketplace .mini_sc_title {
    display: block;
  }
}

.public_user_profile .account_card_marketplace .small_tag {
  font-size: 15px;
  font-weight: 600;
  color: #00adef !important;
}

.public_user_profile .account_card_marketplace .time {
  white-space: nowrap;
}

.public_user_profile .account_card_marketplace .thumbs_text {
  font-size: 14px;
  font-weight: 400;
  font-family: Gilroy-Semibold, sans-serif;
  color: #000 !important;
}

.public_user_profile .account_card_active_listing {
  color: #000 !important;
}

.public_user_profile .account_card_active_listing .mini_card {
  border: 1px solid #********;
  padding: 10px;
  border-radius: 15px;
  margin-bottom: 10px;
}

.public_user_profile .account_card_active_listing .main_inform {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 6px;
}

.public_user_profile .account_card_active_listing .star-rating {
  font-size: 14px;
  font-weight: 600;
}

.public_user_profile .account_card_active_listing .most_recent {
  font-size: 14px;
  font-weight: 600;
  color: #000 !important;
}

.public_user_profile .account_card_active_listing .respon_sell_feedback {
  align-items: start;
  gap: 17px;
}

@media (width <= 900px) {
  .public_user_profile .account_card_active_listing .respon_sell_feedback {
    display: block;
  }

  .public_user_profile .account_card_active_listing .respon_sell_feedback h6 {
    margin-top: 10px;
  }

  .public_user_profile .account_card_active_listing .respon_sell_feedback .activeListing_photo {
    max-width: 100%;
    height: 150px;
  }
}

.public_user_profile .account_card_active_listing .activeListing_photo {
  width: 100%;
  aspect-ratio: 1;
  max-width: 158px;
  border-radius: 5px;
  overflow: hidden;
  flex-shrink: 0;
}

.public_user_profile .account_card_active_listing .activeListing_photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.public_user_profile .account_card_active_listing h6 {
  font-size: 18px;
  font-weight: 400;
  font-family: Gilroy-Semibold, sans-serif;
  color: #000 !important;
}

.public_user_profile .account_card_active_listing .inner_price_text {
  font-size: 15px;
  font-weight: 600;
  color: #000 !important;
}

.public_user_profile .account_card_active_listing .round-border-btn, .public_user_profile .account_card_active_listing .rounded-border-btn {
  border: 1px solid #0003;
  background-color: #0000000d;
  padding: 10px;
  gap: 5px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 50px;
  display: flex;
  transition: all .4s ease-in-out;
}

.public_user_profile .account_card_active_listing .round-border-btn:hover, .public_user_profile .account_card_active_listing .rounded-border-btn:hover {
  border: 1px solid #0000007a;
}

.public_user_profile .account_card_active_listing .rounded-border-btn {
  padding: 10px 18px !important;
}

.public_user_profile .account_card_active_listing .round-bluefill-btn {
  background-color: #00adef;
  color: #fff;
  padding: 5px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 50px;
  transition: all .3s ease-in-out;
}

.public_user_profile .account_card_active_listing .round-bluefill-btn:hover {
  background-color: #0099d1;
}

.public_user_profile .account_card_active_listing .dropdownlist {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-top: 7px;
  padding-bottom: 7px;
  font-size: 14px;
  font-weight: 600;
}

.public_user_profile .account_card_active_listing .dropdownlist img {
  width: 18px !important;
  height: 18px !important;
  min-width: 18px !important;
}

.public_user_profile .account_card_active_listing .dropdownlist span {
  text-align: left;
  flex: 1;
}

.public_user_profile .account_card_dash_listings {
  color: #000 !important;
}

.public_user_profile .account_card_dash_listings .mini_card {
  border: 1px solid #********;
  padding: 10px;
  border-radius: 15px;
  margin-bottom: 10px;
}

.public_user_profile .account_card_dash_listings .main_inform {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 6px;
}

.public_user_profile .account_card_dash_listings .star-rating {
  font-size: 14px;
  font-weight: 600;
}

.public_user_profile .account_card_dash_listings .most_recent {
  font-size: 14px;
  font-weight: 600;
  color: #000 !important;
}

.public_user_profile .account_card_dash_listings .respon_sell_feedback {
  align-items: start;
  gap: 17px;
}

@media (width <= 767px) {
  .public_user_profile .account_card_dash_listings .respon_sell_feedback {
    display: block;
  }

  .public_user_profile .account_card_dash_listings .respon_sell_feedback h6 {
    margin-top: 10px;
  }

  .public_user_profile .account_card_dash_listings .respon_sell_feedback .activeListing_photo {
    max-width: 100%;
    height: 150px;
  }
}

.public_user_profile .account_card_dash_listings .activeListing_photo {
  width: 100%;
  aspect-ratio: 1;
  max-width: 158px;
  border-radius: 5px;
  overflow: hidden;
  flex-shrink: 0;
}

.public_user_profile .account_card_dash_listings .activeListing_photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.public_user_profile .account_card_dash_listings h6 {
  font-size: 18px;
  font-weight: 400;
  font-family: Gilroy-Semibold, sans-serif;
  color: #000 !important;
}

.public_user_profile .account_card_dash_listings .inner_price_text {
  font-size: 15px;
  font-weight: 600;
  color: #000 !important;
}

.public_user_profile .account_card_dash_listings .actions_btn {
  width: 100%;
  justify-content: space-between;
  display: flex;
  margin-top: 50px;
  flex-direction: row-reverse;
  flex-wrap: wrap;
}

@media (width <= 576px) {
  .public_user_profile .account_card_dash_listings .actions_btn {
    margin-top: 12px;
  }
}

.public_user_profile .account_card_dash_listings .actions_btn .first_part {
  position: relative;
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

@media (width <= 576px) {
  .public_user_profile .account_card_dash_listings .actions_btn .first_part {
    justify-content: end;
  }
}

.public_user_profile .account_card_dash_listings .actions_btn .second_part {
  display: flex;
  gap: 8px;
}

@media (width <= 576px) {
  .public_user_profile .account_card_dash_listings .actions_btn .second_part {
    justify-content: end;
    width: 100%;
  }

  .public_user_profile .account_card_dash_listings .actions_btn .second_part button {
    width: 50%;
  }
}

.public_user_profile .account_card_dash_listings .actions_btn .round-border-btn, .public_user_profile .account_card_dash_listings .actions_btn .rounded-border-btn {
  border: 1px solid #0003;
  background-color: #0000000d;
  padding: 6px 12px;
  gap: 5px;
  font-size: 14px;
  font-weight: 600;
  transition: all .4s ease-in-out;
  border-radius: 50px;
  width: auto;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  display: inline-flex;
}

.public_user_profile .account_card_dash_listings .actions_btn .round-border-btn:hover, .public_user_profile .account_card_dash_listings .actions_btn .rounded-border-btn:hover {
  border: 1px solid #0000007a;
}

.public_user_profile .account_card_dash_listings .actions_btn .rounded-border-btn {
  padding: 10px 15px !important;
}

.public_user_profile .account_card_dash_listings .actions_btn .round-bluefill-btn {
  background-color: #00adef;
  color: #fff;
  padding: 6px 18px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 50px;
  transition: all .3s ease-in-out;
  height: 35px;
  width: fit-content;
  align-items: center;
  justify-content: center;
  display: inline-flex;
  white-space: nowrap;
}

.public_user_profile .account_card_dash_listings .actions_btn .round-bluefill-btn:hover {
  background-color: #0099d1;
}

.public_user_profile .account_card_dash_listings .actions_btn .dropdownlist {
  display: flex;
  align-items: center;
  gap: 14px;
  padding-top: 7px;
  padding-bottom: 7px;
  color: #000;
  font-size: 14px;
  font-weight: 600;
}

.public_user_profile .account_card_dash_listings .actions_btn .dropdownlist img {
  width: 18px !important;
  height: 18px !important;
  min-width: 18px !important;
}

.public_user_profile .account_card_dash_listings .actions_btn .dropdownlist span {
  text-align: left;
  flex: 1;
}

.public_user_profile .account_card_dash_listings .actions_btn .dropdownlist:hover {
  color: #00adef;
}

.public_user_profile .account_card_insight {
  color: #000 !important;
}

.public_user_profile .account_card_insight .row {
  border-bottom: 1px solid #0003;
}

.public_user_profile .account_card_insight .row:last-child {
  border-bottom: none;
}

.public_user_profile .account_card_insight .wrap_div {
  display: flex;
  gap: 7px;
  align-items: start;
  padding: 10px 2px;
}

@media (width <= 323px) {
  .public_user_profile .account_card_insight .wrap_div {
    gap: 5px;
  }
}

.public_user_profile .account_card_insight .wrap_div img {
  margin-top: 2.3px;
  flex-shrink: 0;
}

.public_user_profile .account_card_insight p {
  color: #000;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  gap: 10px;
  align-items: start;
}

.public_user_profile .account_card_disputes {
  color: #000 !important;
}

.public_user_profile .account_card_disputes .lg_screen_table table {
  border-collapse: separate;
  width: 100%;
  table-layout: fixed;
  margin-bottom: 0;
}

.public_user_profile .account_card_disputes .lg_screen_table table tr:last-child {
  border-bottom: 0 solid #fff;
}

.public_user_profile .account_card_disputes .lg_screen_table table th {
  color: #fff;
  vertical-align: middle;
  font-size: 14px;
  font-weight: 600;
  background-color: #031940;
  border-radius: 15px;
  padding: 10px;
  border-right: 4px solid #fff;
  border-left: 1.5px solid #fff;
  margin: 0 10px;
}

.public_user_profile .account_card_disputes .lg_screen_table table th .th-inner {
  display: flex;
  gap: 8px;
}

.public_user_profile .account_card_disputes .lg_screen_table table td {
  color: #000;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
  border-right: 1px solid #0003;
  margin-bottom: 5px;
  word-wrap: break-word;
}

.public_user_profile .account_card_disputes .lg_screen_table table td:last-child {
  border-right: none;
}

.public_user_profile .account_card_disputes .lg_screen_table table .view_res_btn {
  font-size: 14px;
  font-weight: 600;
  min-width: 100% !important;
  width: 100% !important;
  min-height: auto !important;
  height: auto !important;
  border-radius: 20px !important;
  padding: 8px 11px !important;
}

.public_user_profile .account_card_disputes .lg_screen_table .bullet-points {
  display: flex;
  align-items: start;
  gap: 8px;
  margin-bottom: 8px;
}

.public_user_profile .account_card_disputes .lg_screen_table .bullet-points img {
  flex-shrink: 0;
  margin-top: 7px;
}

.public_user_profile .account_card_disputes .sm_screen_table .wrap-div {
  border-bottom: 1px solid #0006;
  margin-bottom: 10px;
}

.public_user_profile .account_card_disputes .sm_screen_table .wrap-div:last-child {
  border-bottom: none;
}

.public_user_profile .account_card_disputes .sm_screen_table .row .colunm_head {
  color: #fff;
  background-color: #031940;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
  display: flex;
  gap: 4px;
  border-radius: 15px;
}

.public_user_profile .account_card_disputes .sm_screen_table .row .colunm_value {
  color: #000;
  font-size: 14px;
  font-weight: 600;
  border-right: 1px solid #0003;
  margin-top: 3px;
  margin-bottom: 3px;
  padding: 0 7px;
  word-wrap: break-word;
}

.public_user_profile .account_card_disputes .sm_screen_table .row .colunm_value:last-child, .public_user_profile .account_card_disputes .sm_screen_table .row .colunm_value:nth-child(4) {
  border: none;
}

.public_user_profile .account_card_disputes .sm_screen_table .row .arrow-header {
  width: 30px;
}

.public_user_profile .account_card_disputes .sm_screen_table .bullet-points {
  display: flex;
  align-items: start;
  gap: 8px;
  margin-bottom: 8px;
}

.public_user_profile .account_card_disputes .sm_screen_table .bullet-points img {
  flex-shrink: 0;
  margin-top: 7px;
}

.public_user_profile .account_card_disputes .no_disputes {
  font-size: 18px;
  font-weight: 600;
  color: #000;
}

.public_user_profile .account_card_disputes .table_form_textarea {
  background-color: #4f5e7a;
  border-radius: 6px;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  width: 100%;
  padding: 5px 8px;
}

.public_user_profile .account_card_disputes .table_form_textarea:focus {
  outline: none;
  background-color: #576887;
}

.public_user_profile .account_card_disputes .character-count {
  text-align: end;
  margin-top: 2px;
  color: #4f5e7a;
  font-size: 14px;
}

.public_user_profile .account_card_disputes .file-upload-wrapper {
  display: inline-block;
  width: 100%;
  border: 2px solid #ddd;
  border-radius: 9999px;
  padding: 6px 20px;
  font-size: 14px;
  font-weight: 600;
  background-color: #fff;
  color: #000;
  cursor: pointer;
  transition: background-color .2s;
  height: 37px;
  text-align: center;
}

.public_user_profile .account_card_disputes .file-upload-wrapper:hover {
  background-color: #f0f0f0;
}

.public_user_profile .account_card_disputes .file-upload-wrapper input[type="file"] {
  display: none;
}

.public_user_profile .account_card_timeline {
  color: #000 !important;
}

.public_user_profile .account_card_timeline h2 {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  padding: 0;
  background-color: #031940;
  border-radius: 15px;
  padding: 12px;
  display: inline-flex;
}

.public_user_profile .account_card_timeline ul {
  padding-left: 17px;
  margin: 13px 0;
}

.public_user_profile .account_card_timeline ul li {
  color: #000;
  font-size: 14px;
  font-weight: 600;
  border-radius: 15px;
  list-style-type: circle;
}

.public_user_profile .account_card_create_listing .form-wrapper {
  width: 800px;
  margin: 0 auto;
}

.public_user_profile .account_card_create_listing .form-wrapper .form-input, .public_user_profile .account_card_create_listing .form-wrapper .form-select, .public_user_profile .account_card_create_listing .form-wrapper .form-textarea {
  padding: 1rem;
  border-radius: 15px;
  border: none;
  width: 100%;
  color: #000;
  font-size: 18px;
  margin-bottom: 12px;
}

@media (width <= 501px) {
  .public_user_profile .account_card_create_listing .form-wrapper .form-input, .public_user_profile .account_card_create_listing .form-wrapper .form-select, .public_user_profile .account_card_create_listing .form-wrapper .form-textarea {
    font-size: 15px;
  }
}

.public_user_profile .account_card_create_listing .form-wrapper .form-input::placeholder, .public_user_profile .account_card_create_listing .form-wrapper .form-select::placeholder, .public_user_profile .account_card_create_listing .form-wrapper .form-textarea::placeholder {
  color: #000c;
}

.public_user_profile .account_card_create_listing .form-wrapper .form-input:focus, .public_user_profile .account_card_create_listing .form-wrapper .form-select:focus, .public_user_profile .account_card_create_listing .form-wrapper .form-textarea:focus {
  outline: none;
}

.public_user_profile .account_card_create_listing .form-wrapper .tags-input {
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  padding: .8rem 1rem;
  border-radius: 15px;
  margin-bottom: 12px;
  gap: 10px;
}

.public_user_profile .account_card_create_listing .form-wrapper .tags-input input {
  color: #000;
  width: 100%;
  font-size: 18px;
  border: none;
}

@media (width <= 501px) {
  .public_user_profile .account_card_create_listing .form-wrapper .tags-input input {
    font-size: 15px;
  }
}

.public_user_profile .account_card_create_listing .form-wrapper .tags-input input::placeholder {
  color: #000c;
}

.public_user_profile .account_card_create_listing .form-wrapper .tags-input input:focus {
  outline: none;
}

.public_user_profile .account_card_create_listing .form-wrapper .tags-input .tag {
  background-color: #e6e6e6;
  color: #000;
  display: flex;
  align-items: center;
  font-size: 18px;
  padding: 5px 9px !important;
  border-radius: 50px !important;
  gap: 12px !important;
}

.public_user_profile .account_card_create_listing .form-wrapper .tags-input .tag svg {
  cursor: pointer;
  font-weight: 700;
}

.public_user_profile .account_card_create_listing .form-wrapper .checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: .6rem;
  margin-bottom: 11px;
}

.public_user_profile .account_card_create_listing .form-wrapper .checkbox-wrapper .custom_checkbox_input {
  height: 20px;
  width: 20px;
}

.public_user_profile .account_card_create_listing .form-wrapper .checkbox-wrapper .custom_checkbox_input:checked {
  background-color: #00adef !important;
  border: 1px solid #00adef !important;
}

.public_user_profile .account_card_create_listing .form-wrapper .file-upload-wrapper {
  display: flex;
  width: 100%;
  height: 46px;
  align-items: center;
  justify-content: center;
  border: 2px solid #ddd;
  border-radius: 9999px;
  padding: 6px 20px;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  background-color: #fff;
  color: #000;
  cursor: pointer;
  transition: background-color .2s;
}

@media (width <= 501px) {
  .public_user_profile .account_card_create_listing .form-wrapper .file-upload-wrapper {
    font-size: 15px;
  }
}

.public_user_profile .account_card_create_listing .form-wrapper .file-upload-wrapper:hover {
  background-color: #f0f0f0;
}

.public_user_profile .account_card_create_listing .form-wrapper .file-upload-wrapper input[type="file"] {
  display: none;
}

.public_user_profile .account_card_create_listing .form-wrapper .character-count {
  font-size: .9rem;
  font-weight: 600;
  color: #000c;
  position: absolute;
  bottom: 21px;
  right: 11px;
}

.public_user_profile .account_card_create_listing .form-wrapper .upload-container {
  margin-bottom: 20px;
}

.public_user_profile .account_card_create_listing .form-wrapper .upload-container .upload-box {
  background: #fff;
  border-radius: 12px;
  padding: .5rem;
  text-align: center;
  cursor: pointer;
  position: relative;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 20px;
  font-weight: 600;
}

.public_user_profile .account_card_create_listing .form-wrapper .upload-container .upload-placeholder img {
  margin: 0 auto;
}

.public_user_profile .account_card_create_listing .form-wrapper .upload-container .upload-placeholder p {
  font-size: 20px;
  font-weight: 600;
  margin: .35rem 0 0;
  color: #000;
}

.public_user_profile .account_card_create_listing .form-wrapper .upload-container .upload-placeholder .sub-text {
  margin: 0;
  font-size: 15px !important;
}

.public_user_profile .account_card_create_listing .form-wrapper .upload-container .preview {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: hidden;
}

.public_user_profile .account_card_create_listing .form-wrapper .upload-container .preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.public_user_profile .account_card_create_listing .form-wrapper .upload-container .remove-btn {
  position: absolute;
  top: 50%;
  right: 50%;
  transform: translate(-50%, -50%);
  background: #ff4d4f;
  border: none;
  color: #fff;
  font-weight: bold;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  cursor: pointer;
  font-size: 14px;
}

.public_user_profile .account_card_create_listing .form-wrapper .upload-container .image-count {
  margin: .5rem 0;
  font-size: 15px;
  font-weight: 600;
  color: #fff;
}

.public_user_profile .account_card_create_listing .form-wrapper .form-multi-select {
  color: #000;
  font-size: 18px;
  border-radius: 15px;
}

.public_user_profile .account_card_create_listing .form-wrapper .select__control {
  padding: 9px;
  border-radius: 15px;
  font-size: 18px;
}

.public_user_profile .account_card_create_listing .form-wrapper .select__placeholder {
  color: #000 !important;
}

.public_user_profile .account_card_create_listing .form-wrapper .select__multi-value {
  border-radius: 20px;
  padding: 2px 6px;
  font-size: 18px;
}

.public_user_profile .account_card_create_listing .form-wrapper .select__multi-value__label {
  font-size: 18px;
  border-radius: 5px;
}

.public_user_profile .account_card_create_listing .form-wrapper .select__multi-value__remove {
  color: #000;
  border-radius: 50%;
  padding-left: 9px;
  padding-right: 9px;
}

.public_user_profile .account_card_create_listing .form-wrapper .select__multi-value__remove:hover {
  background-color: #d3d3d3;
  border-radius: 50%;
  padding-left: 9px;
  padding-right: 9px;
}

.public_user_profile .modal_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #0009;
  z-index: 9998;
}

.public_user_profile .modal_overlay .search_section, .public_user_profile .modal_overlay .modal-body {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 1000px;
  background-color: #031940;
  padding: 20px;
  border-radius: 15px;
  max-height: 90vh;
  border: 1px solid #00adef;
}

@media (width <= 1023px) {
  .public_user_profile .modal_overlay .search_section, .public_user_profile .modal_overlay .modal-body {
    width: 90%;
  }
}

.public_user_profile .modal_overlay .search_section h4, .public_user_profile .modal_overlay .modal-body h4 {
  font-size: 28px;
  font-weight: 400;
  margin-bottom: 20px;
}

.public_user_profile .modal_overlay .search_section .modal-footer-btn, .public_user_profile .modal_overlay .modal-body .modal-footer-btn {
  font-size: 18px;
  min-height: 40px !important;
}

.public_user_profile .modal_overlay .search_section .common_whitecard_innerbody, .public_user_profile .modal_overlay .modal-body .common_whitecard_innerbody {
  max-height: calc(90vh - 170px);
  overflow-y: scroll;
}


/* [project]/src/css/account/AccountLayout.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.Account_layout_main {
  display: flex;
}

.Account_layout_rightaside {
  width: calc(100% - 355px);
  padding: 2.813rem 1.875rem;
  color: #fff;
  position: relative;
}

@media (width >= 992px) {
  .Account_layout_rightaside .mb-lg-4 {
    margin-bottom: 1.875rem !important;
  }
}

@media (width <= 1199px) {
  .Account_layout_rightaside {
    width: 100%;
    padding: 2.813rem 1rem;
  }
}

.sidebar_heading {
  width: 100%;
  margin-bottom: 26px;
}

@media screen and (width <= 1199px) {
  .sidebar_heading {
    padding-left: 45px;
  }
}

.sidebar_heading_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.sidebar_heading h2 {
  font-size: 48px;
  font-weight: 400;
  font-family: Gilroy-Bold, sans-serif;
  display: inline-flex;
}

@media (width <= 1199px) {
  .sidebar_heading h2 {
    font-size: 1.688rem;
  }
}

@media (width <= 767px) {
  .sidebar_heading h2 {
    font-size: 1.5rem;
  }
}

.sidebar_heading_icon button {
  background-color: #0000;
  border: 0;
  color: #00adef;
  font-size: 1.15rem;
  padding: 0;
  font-weight: 600;
  transition: all .3s ease-in-out;
}

@media (width <= 991px) {
  .sidebar_heading_icon button {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.sidebar_heading_icon button .me-2 {
  margin-right: 10px !important;
}

.sidebar_heading_icon button .ms-2 {
  margin-left: 10px !important;
}

.sidebar_heading_icon button svg {
  margin-left: 10px;
  transition: all .3s ease-in-out;
  width: 16px;
}

@media (width <= 991px) {
  .sidebar_heading_icon button svg {
    width: 14px;
  }
}

@media (width <= 767px) {
  .sidebar_heading_icon button svg {
    width: 10px;
    margin-left: 0;
  }
}

.sidebar_heading_icon button svg path {
  fill: #00adef;
}

.sidebar_heading_icon button:hover {
  color: #fea500;
}

.sidebar_heading_icon button:hover svg {
  margin-left: 12px;
}

@media (width <= 991px) {
  .sidebar_heading_icon button:hover svg {
    margin-left: 10px;
  }
}

.sidebar_heading_icon button:hover svg path {
  fill: #fea500;
}

.common_blackcard, .common_whitecard {
  background-color: #191c23;
  padding: 10px;
  border-radius: 15px;
  border: 1px solid #04498c;
  width: 100%;
  height: 100%;
}

.common_blackcard_innerheader, .common_whitecard_innerheader {
  background-color: #031940;
  padding: .625rem 1.25rem;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: Gilroy-Semibold, sans-serif;
  font-weight: 400;
}

@media (width <= 991px) {
  .common_blackcard_innerheader, .common_whitecard_innerheader {
    padding: .625rem;
  }
}

.common_blackcard_innerheader p, .common_whitecard_innerheader p {
  color: #fff;
}

@media (width <= 991px) {
  .common_blackcard_innerheader p, .common_whitecard_innerheader p {
    font-size: 14px;
  }
}

@media (width <= 767px) {
  .common_blackcard_innerheader p, .common_whitecard_innerheader p {
    font-size: 14px;
  }
}

.common_blackcard_innerheader_content, .common_whitecard_innerheader_content {
  padding-right: 30px;
  font-size: 18px;
  font-weight: 400;
}

.common_blackcard_innerheader_content h6, .common_whitecard_innerheader_content h6 {
  font-weight: 600;
}

@media (width <= 991px) {
  .common_blackcard_innerheader_content, .common_whitecard_innerheader_content {
    padding-right: 15px;
  }

  .common_blackcard_innerheader_content h6, .common_whitecard_innerheader_content h6 {
    font-size: 18px !important;
  }
}

@media (width <= 767px) {
  .common_blackcard_innerheader_content, .common_whitecard_innerheader_content {
    padding-right: 5px;
  }
}

@media (width <= 400px) {
  .common_blackcard_innerheader_content h6, .common_whitecard_innerheader_content h6 {
    font-size: 14px !important;
  }
}

.common_blackcard_innerheader_icon button, .common_whitecard_innerheader_icon button {
  background-color: #0000;
  border: 0;
  color: #00adef;
  font-size: 18px;
  padding: 0;
  font-weight: 600;
  font-family: Gilroy-Semibold, sans-serif;
  transition: all .3s ease-in-out;
  white-space: nowrap;
}

@media (width <= 991px) {
  .common_blackcard_innerheader_icon button, .common_whitecard_innerheader_icon button {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.common_blackcard_innerheader_icon button .me-2, .common_whitecard_innerheader_icon button .me-2 {
  margin-right: 10px !important;
}

.common_blackcard_innerheader_icon button .ms-2, .common_whitecard_innerheader_icon button .ms-2 {
  margin-left: 10px !important;
}

.common_blackcard_innerheader_icon button .link-icon, .common_whitecard_innerheader_icon button .link-icon {
  flex-shrink: 0;
  height: auto;
}

.common_blackcard_innerheader_icon button svg, .common_whitecard_innerheader_icon button svg {
  transition: all .3s ease-in-out;
  width: 16px;
}

@media (width <= 991px) {
  .common_blackcard_innerheader_icon button svg, .common_whitecard_innerheader_icon button svg {
    width: 14px;
  }
}

@media (width <= 767px) {
  .common_blackcard_innerheader_icon button svg, .common_whitecard_innerheader_icon button svg {
    width: 10px;
    margin-left: 0;
  }
}

.common_blackcard_innerheader_icon button svg path, .common_whitecard_innerheader_icon button svg path {
  fill: #00adef;
}

.common_blackcard_innerheader_icon button:hover, .common_whitecard_innerheader_icon button:hover {
  color: #fea500;
}

.common_blackcard_innerheader_icon button:hover svg, .common_whitecard_innerheader_icon button:hover svg {
  margin-left: 12px;
}

@media (width <= 991px) {
  .common_blackcard_innerheader_icon button:hover svg, .common_whitecard_innerheader_icon button:hover svg {
    margin-left: 10px;
  }
}

.common_blackcard_innerheader_icon button:hover svg path, .common_whitecard_innerheader_icon button:hover svg path {
  fill: #fea500;
}

.common_blackcard_innerheader_tradeacct, .common_whitecard_innerheader_tradeacct {
  min-width: 200px;
  width: 200px;
}

.common_blackcard_innerheader_tradeacct h6, .common_whitecard_innerheader_tradeacct h6 {
  background-color: #04498c;
  padding: 8px 15px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  color: #fff;
  font-size: 1.25rem;
  line-height: normal;
}

@media (width <= 991px) {
  .common_blackcard_innerheader_tradeacct h6, .common_whitecard_innerheader_tradeacct h6 {
    font-size: 16px;
  }
}

.common_blackcard_innerheader_tradeacct p, .common_whitecard_innerheader_tradeacct p {
  background-color: #fff;
  padding: 8px 15px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  color: #000;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  line-height: normal;
}

@media (width <= 991px) {
  .common_blackcard_innerheader_tradeacct p, .common_whitecard_innerheader_tradeacct p {
    font-size: 16px;
  }
}

.common_blackcard_innerbody, .common_whitecard_innerbody {
  padding: 1.25rem .625rem;
}

.connetionTable .common_blackcard_innerbody, .connetionTable .common_whitecard_innerbody, .removePadding .common_blackcard_innerbody, .removePadding .common_whitecard_innerbody {
  padding: 0;
}

@media (width <= 991px) {
  .account_card.pullcontent .common_blackcard_innerheader_icon, .account_card.pullcontent .common_whitecard_innerheader_icon {
    margin-top: 10px;
  }
}

.common_whitecard {
  background-color: #fff !important;
}

.common_whitecard_innerbody {
  padding: .8rem .3rem 0;
}

.account_card .label {
  color: #fff;
  font-size: 1.125rem;
  font-weight: 600;
}

.account_card_list_btns {
  display: flex;
  justify-content: end;
  align-items: center;
  gap: 10px;
}

.account_card_list_btns .btn-style {
  min-height: 40px;
  font-size: 16px !important;
}

.account_card_list_form {
  width: 40%;
}

.account_card_list_form .row {
  width: 100% !important;
}

.account_card_list ul li {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.25rem;
  color: #fff;
  font-size: 1.125rem;
  font-weight: 600;
}

@media (width <= 991px) {
  .account_card_list ul li {
    font-size: 15px;
    margin-bottom: 1rem;
  }
}

.account_card_list ul li:last-child {
  margin-bottom: 0;
}

.account_card_list ul li span {
  color: #fff;
}

.account_card_list p {
  color: #c5c5c5;
}

.account_card_redeem .form-control {
  width: calc(100% - 136px);
  margin-right: 10px;
}

.account_card_redeem ::placeholder {
  color: #fff;
  opacity: 1;
}

.account_card_redeem .btn-style {
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  min-width: 150px;
  padding: 8px 15px;
  min-height: 56px;
}

@media (width <= 991px) {
  .account_card_redeem .btn-style {
    font-size: 14px;
    line-height: 24px;
    min-width: 130px;
    padding: 8px 15px;
    min-height: 52px;
  }
}

.account_card_redeem .error-messages .success {
  color: #32cd33;
}

.account_card_redeem .error-messages .invalid {
  color: #ff696a;
}

.account_card_checkup_verify {
  width: calc(100% - 150px);
  padding-right: 30px;
}

@media (width <= 991px) {
  .account_card_checkup_verify {
    width: calc(100% - 100px);
    padding-right: 0;
  }
}

.account_card_checkup_chart {
  width: 150px;
  text-align: center;
}

@media (width <= 991px) {
  .account_card_checkup_chart {
    width: 100px;
  }

  .account_card_checkup_chart .CircularProgressbar_text h6 {
    font-size: 14px;
  }
}

.account_card_subscription_list {
  display: flex;
  flex-wrap: wrap;
}

.account_card_subscription_list li {
  width: 25%;
}

@media (width <= 767px) {
  .account_card_subscription_list li {
    width: 50%;
    padding: 5px 0;
  }

  .account_card_subscription_list li:nth-child(2n) {
    text-align: right;
  }
}

.account_card_subscription_list li p {
  color: #fff;
  font-weight: 600;
  margin-top: .5rem;
}

@media (width <= 991px) {
  .account_card_subscription_list li p {
    font-size: 14px;
    line-height: 20px;
  }
}

@media (width <= 767px) {
  .account_card_subscription_list li p {
    margin-top: 0;
  }
}

@media (width <= 991px) {
  .account_card_subscription_list li h6 {
    font-size: 15px;
  }
}

.account_card_table .simple_table_imgIcon {
  min-width: 20px;
}

.account_card_table .tableless {
  border: 0;
}

.account_card_table .tableless:before {
  display: none;
}

.account_card_table .tableless .common_table tr th, .account_card_table .tableless .common_table tr td {
  border: 0;
}

.account_card_table .tableless .common_table tr td {
  border-bottom: 1px solid #666;
  color: #fff;
  font-weight: 600 !important;
}

.account_card_table .tableless .common_table thead tr {
  border-radius: 15px;
}

.account_card_table .tableless .common_table thead tr th {
  background-color: #031940;
  padding: 10px 15px;
  font-size: 1rem;
}

.account_card_table .tableless .common_table thead tr th:first-child {
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}

.account_card_table .tableless .common_table thead tr th:last-child {
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}

.account_card_table .table tr td {
  padding: 1rem .75rem;
  vertical-align: middle;
  color: #fff;
}

@media (width <= 767px) {
  .account_card_table .table tr td {
    padding: .75rem .5rem;
    font-size: .875rem;
  }
}

.account_card_table .table tr td .fw-bold {
  font-weight: 600 !important;
}

.account_card_table .table tr td .text-muted {
  font-size: .75rem;
  margin-top: .25rem;
  color: #fff9 !important;
}

.account_card_table .table tr td .yellow_text {
  color: #fea500;
}

@media (width <= 767px) {
  .account_card_table .table td:first-child {
    width: 15%;
  }

  .account_card_table .table td:nth-child(2) {
    width: 20%;
  }

  .account_card_table .table td:nth-child(3) {
    width: 15%;
  }

  .account_card_table .table td:nth-child(4) {
    width: 15%;
  }

  .account_card_table .table td:nth-child(5) {
    width: 15%;
  }

  .account_card_table .table td:nth-child(6) {
    width: 20%;
  }
}

@media (width <= 576px) {
  .account_card_table .table {
    font-size: .75rem;
  }

  .account_card_table .table td {
    padding: .5rem .25rem;
  }

  .account_card_table .table td:last-child .subscription-button {
    max-width: 150px;
    font-size: .75rem;
    width: 100% !important;
  }
}

.account_card .add_phone_number, .account_card .add_number, .account_card .blue_text_btn {
  background-color: #0000;
  border: 0;
  color: #00adef;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 24.5px;
  letter-spacing: -.1px;
  transition: all .3s ease-in-out;
  padding: 0;
}

@media (width <= 991px) {
  .account_card .add_phone_number, .account_card .add_number, .account_card .blue_text_btn {
    font-size: 1rem;
  }
}

.account_card .add_phone_number:hover, .account_card .add_number:hover, .account_card .blue_text_btn:hover {
  color: #fea500;
}

.account_card .add_number svg, .account_card .add_number img, .account_card .blue_text_btn svg, .account_card .blue_text_btn img {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.account_card .add_number svg path, .account_card .add_number img path, .account_card .blue_text_btn svg path, .account_card .blue_text_btn img path {
  fill: #00adef;
}

.account_card .add_number:hover svg path, .account_card .blue_text_btn:hover svg path {
  fill: #fea500;
}

.CircularProgressbar_text h6 {
  font-size: 14px !important;
}

.account-custom-select {
  position: relative;
}

.account-custom-select .header {
  min-height: 56px;
  box-shadow: none;
  outline: none;
  width: 100%;
  padding: .5rem 1.25rem;
  border-radius: 1rem;
  background-color: #ffffff4d;
  color: #fff;
  font-size: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.account-custom-select .header span {
  width: 100% !important;
}

.account-custom-select .body {
  width: 100%;
  padding: .5rem 1.25rem;
  position: absolute;
  top: 65px;
  border-radius: 1rem;
  border: 1px solid #ffffff4d;
  background-color: #191c23;
  max-height: 250px;
  overflow-y: scroll;
  z-index: 999;
}

.account-custom-select .body .search {
  background-color: #ffffff4d;
  margin: .5rem 0;
  padding: .5rem 1.25rem;
  width: 100%;
  border-radius: 1rem;
  display: flex;
  gap: 10px;
  align-items: center;
}

.account-custom-select .body .search input {
  background-color: #0000;
  width: 100%;
  color: #fff;
}

.account-custom-select .body .search input:focus-visible {
  outline: none !important;
}

.account-custom-select .body ul li {
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  border-bottom: 0 !important;
  margin-bottom: 0 !important;
}

.account-custom-select .body ul li:hover {
  background-color: #ffffff4d;
}

.account-custom-select.simple .header {
  border: 1px solid #0003;
  background-color: #f2f2f2;
  min-height: 34px;
  padding: 7px 10px;
  border-radius: 10px;
}

.account-custom-select.simple .header span {
  font-size: 14px;
  font-weight: 600;
  color: #000 !important;
}

.account-custom-select.simple .body {
  width: 396px;
  padding: 10px 12px;
  top: 45px;
  right: 0;
  border-radius: .94rem;
  border: 1px solid #0606061a;
  background-color: #fff;
  max-height: 200px;
}

@media only screen and (width <= 500px) {
  .account-custom-select.simple .body {
    width: auto;
  }
}

.account-custom-select.simple .body.align-left {
  left: 0;
  right: auto;
}

.account-custom-select.simple .body.align-right {
  right: 0;
  left: auto;
}

.account-custom-select.simple .body::-webkit-scrollbar {
  display: block;
}

.account-custom-select.simple .body .option-heading {
  display: flex;
  align-items: center;
  gap: 5px;
  margin: 6px 0 2px;
}

.account-custom-select.simple .body .option-heading p {
  color: #000;
  font-size: 14px;
  font-weight: 700;
}

.account-custom-select.simple .body ul li {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 0;
  border-bottom: 1px solid #0003;
  border-radius: 0 !important;
}

.account-custom-select.simple .body ul li:hover {
  background-color: #d3d3d32a;
}

.width-autofit {
  width: fit-content !important;
}

.text_00ADEF {
  color: #00adef !important;
}

input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  transition: all .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background-color: #9c9a9f;
  transition: all .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #0099d1;
}

input:checked + .slider:before {
  transform: translateX(22px);
  background-color: #fff;
}


/* [project]/src/css/common/CommonSearch.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.commonSearch {
  display: flex;
  position: relative;
  align-items: center;
}

.commonSearch.searchBtn .form-control {
  padding: .5rem 1rem;
  background-color: #fff;
  color: #000;
  border: 0;
  border-top-left-radius: 10rem;
  border-bottom-left-radius: 10rem;
  min-height: 50px;
  width: calc(100% - 54px);
  min-width: auto;
  font-weight: 600;
}

.commonSearch.searchBtn .form-control::placeholder {
  color: #c5c5d5;
  opacity: 1;
}

.commonSearch.searchBtn .form-control:focus {
  box-shadow: none;
  outline: 0;
  background-color: #fff;
  color: #000;
}

.commonSearch .form-control {
  padding: .5rem 1rem;
  padding-left: 50px;
  background-color: #ffffff4d;
  color: #fff;
  border: 0;
  border-radius: 15px;
  min-height: 70px;
  width: auto;
  width: 400px;
  font-size: 1.25rem;
  appearance: none;
  -webkit-appearance: none;
}

@media (width <= 991px) {
  .commonSearch .form-control {
    font-size: 16px;
    min-height: 56px;
    padding-left: 40px;
  }
}

.commonSearch .form-control:hover {
  appearance: none;
}

.commonSearch .form-control::placeholder {
  color: #fffc;
  opacity: 1;
}

.commonSearch .form-control:disabled {
  background-color: #0000;
}

.commonSearch .form-control:focus {
  box-shadow: none;
  outline: 0;
  background-color: #ffffff4d;
  color: #fff;
  border: 0;
}

.commonSearch .onlyIcon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

@media (width <= 991px) {
  .commonSearch .onlyIcon {
    left: 15px;
  }
}

.commonSearch .btnIcon {
  cursor: pointer;
  width: 54px;
  min-height: 50px;
  background-color: #00adef;
  border-top-right-radius: 10rem;
  border-bottom-right-radius: 10rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all .3s ease-in-out;
}

.commonSearch .btnIcon:hover {
  background-color: #fea500;
}


/*# sourceMappingURL=src_css_63d73a02._.css.map*/
