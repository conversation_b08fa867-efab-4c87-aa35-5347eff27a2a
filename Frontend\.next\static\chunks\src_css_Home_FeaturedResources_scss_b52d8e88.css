/* [project]/src/css/Home/FeaturedResources.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.featured_resources {
  background-color: #031940;
  padding: 3rem;
  padding-bottom: 100px;
}

@media screen and (width <= 767px) {
  .featured_resources {
    padding: 3rem 0;
    font-size: 1rem;
  }
}

.featured_resources_heading {
  margin-bottom: 70px;
}

@media screen and (width <= 767px) {
  .featured_resources_heading {
    margin-bottom: 30px;
    text-align: center;
  }

  .featured_resources_content {
    text-align: center;
  }
}

.featured_resources_content p {
  font-size: 21px;
  font-weight: 600;
  line-height: 34px;
  margin: 1.5rem 0 2rem;
  color: #fff;
}

@media screen and (width <= 767px) {
  .featured_resources_content p {
    font-size: 16px;
    line-height: 24px;
  }
}

.featured_resources_content h3 {
  font-weight: 800;
  width: 100%;
  font-size: 3rem;
}

@media (width <= 991px) {
  .featured_resources_content h3 {
    font-size: 35px;
  }
}

@media (width <= 767px) {
  .featured_resources_content h3 {
    font-size: 1.5rem;
  }
}

/*# sourceMappingURL=src_css_Home_FeaturedResources_scss_b52d8e88.css.map*/