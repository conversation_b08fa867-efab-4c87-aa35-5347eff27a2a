/* [project]/src/css/dashboard/CommonHead.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.common_head_container {
  position: sticky;
  top: 80px;
  bottom: 50px;
  z-index: 999;
}

@media (width <= 1199px) {
  .common_head_container {
    top: 77.3px;
  }
}

@media (width <= 767px) {
  .common_head_container {
    top: 64.16px;
  }
}

@media (width <= 360px) {
  .common_head_container {
    top: 64.16px;
  }
}

.common_head {
  background-color: #031940;
  padding: 1.25rem;
  border-radius: 1.25rem;
  border-bottom: 1px solid #064197;
}

@media (width <= 1199px) {
  .common_head {
    padding: 15px;
  }
}

@media (width <= 767px) {
  .common_head {
    border: 1px solid #064197;
  }
}

@media (width <= 550px) {
  .common_head_md {
    display: none;
  }
}

.common_head_small {
  display: none;
}

.common_head_small button {
  min-width: 30px;
  min-height: 30px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #00adef;
}

@media (width <= 550px) {
  .common_head_small button {
    min-width: 25px;
    min-height: 25px;
  }

  .common_head_small button svg {
    height: 15px !important;
    width: 15px !important;
  }
}

.common_head_small button svg {
  height: 20px;
  width: 20px;
}

.common_head_small .layout_fix {
  display: flex;
  justify-content: space-between;
}

@media (width <= 550px) {
  .common_head_small {
    display: block;
  }
}

.common_head .commom_tradeacct {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.common_head .commom_tradeacct .account {
  background-color: #04498c;
  padding: 8px 15px;
  height: 100%;
  width: 100%;
  border-radius: 10px 10px 0 0;
}

.common_head .commom_tradeacct .account h6 {
  color: #fff;
  font-size: 1rem;
  line-height: 1;
}

@media (width <= 1199px) {
  .common_head .commom_tradeacct .account h6 {
    font-size: 14px;
  }
}

.common_head .commom_tradeacct .number, .common_head .commom_tradeacct select {
  background-color: #fff;
  padding: 5px 15px;
  height: 100%;
  width: 100%;
  border-radius: 0 0 10px 10px;
  font-size: 1rem;
  font-weight: 600;
}

@media (width <= 1199px) {
  .common_head .commom_tradeacct .number, .common_head .commom_tradeacct select {
    font-size: 14px;
  }
}

.common_head .commom_tradeacct .form-select:focus {
  border-color: #0000 !important;
  box-shadow: none !important;
}

.common_head_search .commonSearch .form-control {
  width: 100%;
  border: 0;
}

@media (width <= 1199px) {
  .common_head_search .commonSearch .form-control {
    min-height: 66px;
  }
}

@media (width <= 767px) {
  .common_head_search .commonSearch .form-control {
    min-height: 60px;
  }
}

.common_head_datebox {
  background-color: #ffffff4d;
  min-height: 70px;
  border-radius: 1rem;
  padding: 8px 15px;
}

@media (width <= 1199px) {
  .common_head_datebox {
    padding: 8px 10px;
    min-height: 66px;
  }
}

@media (width <= 767px) {
  .common_head_datebox {
    min-height: auto;
  }
}

.common_head_datebox h6 {
  color: #fff9;
  padding-top: 5px;
}

@media (width <= 1199px) {
  .common_head_datebox h6 {
    font-size: 14px;
  }

  .common_head_datebox h5 {
    font-size: 16px;
  }
}

.head_draft {
  margin-top: 10px;
}

.head_draft_icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.head_draft_icons p {
  font-weight: 600;
}

.head_draft_icons svg, .head_draft_icons img {
  width: 20px;
  height: 20px;
}

/*# sourceMappingURL=src_css_dashboard_CommonHead_scss_b52d8e88.css.map*/