/* [project]/src/css/common/textInput.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.form-label, .form-check-label {
  display: block;
  font-size: 1rem;
  font-weight: 400;
  line-height: normal;
  margin-bottom: .625rem;
  color: #fff !important;
}

@media (width <= 1199px) {
  .form-label, .form-check-label {
    font-size: .875rem;
  }
}

.spanInputCounter {
  position: absolute;
  right: 7px;
  top: 39%;
  font-size: 14px;
}

.customInput {
  margin-bottom: 1.25rem;
  line-height: normal;
}

@media (width <= 767px) {
  .customInput {
    margin-bottom: 1rem;
  }
}

.customInput_inner {
  position: relative;
}

.customInput_inner .allocP {
  position: absolute;
  top: 13px;
  right: 1rem;
  color: #9c9a9f;
  font-size: 1rem;
  font-weight: 500;
}

.customInput label {
  display: block;
  color: #9c9a9f;
  font-size: 1rem;
  font-weight: 400;
  line-height: normal;
  margin-bottom: .625rem;
}

@media (width <= 1199px) {
  .customInput label {
    font-size: .875rem;
  }
}

.customInput .form-control {
  min-height: 56px;
  box-shadow: none;
  outline: none;
  width: 100%;
  padding: .5rem 1.25rem;
  border-radius: 1rem;
  border: 1px solid #ffffff4d;
  background-color: #ffffff4d;
  color: #fff;
  font-size: 1rem;
}

@media (width <= 1599px) {
  .customInput .form-control {
    min-height: 52px;
    font-size: 1rem;
  }
}

.customInput .form-control:hover {
  appearance: none;
}

.customInput .form-control::placeholder {
  color: #fff;
  opacity: .7;
}

.customInput .form-control:disabled {
  background-color: #0000;
}

.customInput .form-control:focus {
  box-shadow: none;
  border: 1px solid #ffffff4d;
  background-color: #ffffff4d;
  color: #fff;
}

.customInput .form-control.passwordInput {
  padding-right: 4.375rem;
}

.customInput.passwordInput .form-control {
  padding-right: 3.75rem;
}

.customInput .eyeIcon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
}

.customInput .eyeIcon svg {
  width: 1.25rem;
  height: 1.25rem;
}

.customInput .eyeIcon svg path {
  fill: #fff;
}

.checkbox_input .form-check {
  margin-bottom: 0;
  padding: 0;
  display: flex;
  align-items: center;
}

.checkbox_input .form-check .form-check-input {
  float: unset;
  margin: 0;
  box-shadow: none;
  cursor: pointer;
  background-color: #0000;
  border: 1px solid #666;
  width: 20px !important;
  height: 20px !important;
}

.checkbox_input .form-check .form-check-input:checked {
  border-color: #00adef;
}

.checkbox_input .form-check .form-check-input:checked[type="radio"] {
  background-size: 10px;
}

.checkbox_input .form-check .form-check-label {
  line-height: 1;
  margin-left: 10px;
}

.checkbox_input .form-check.form-switch .form-check-input {
  background-color: #fff;
  border-color: #666;
  background-size: 30px;
  width: 72px !important;
  height: 34px !important;
}

.checkbox_input .form-check.form-switch .form-check-input:checked {
  background-color: #00adef;
  border-color: #00adef;
}

.error-field {
  border: 1px solid #ff696a !important;
}

.error-message {
  color: #ff696a;
  font-size: 16px;
  line-height: normal;
  display: block;
  padding-top: 5px;
  font-weight: 700;
}

.form-control {
  min-height: 56px;
  box-shadow: none;
  outline: none;
  width: 100%;
  padding: .5rem 1.25rem;
  border-radius: 1rem;
  border: 1px solid #ffffff4d;
  background-color: #ffffff4d;
  color: #000;
  font-size: 1rem;
}

@media (width <= 1599px) {
  .form-control {
    min-height: 52px;
    font-size: 1rem;
  }
}

.form-control:hover {
  appearance: none;
}

.form-control::placeholder {
  color: #fff;
}

.form-control:disabled {
  background-color: #0000;
}

.form-control:focus {
  box-shadow: none;
  border: 1px solid #ffffff4d;
  background-color: #ffffff4d;
  color: #fff;
}

.form-control.passwordInput {
  padding-right: 4.375rem;
}

.inputLabel {
  font-size: 15px;
  font-weight: 600;
  color: #fffc;
  margin-bottom: 10px;
}

/*# sourceMappingURL=src_css_common_textInput_scss_b52d8e88.css.map*/