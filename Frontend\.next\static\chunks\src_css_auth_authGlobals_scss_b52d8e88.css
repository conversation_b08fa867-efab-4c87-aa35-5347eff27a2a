/* [project]/src/css/auth/authGlobals.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.loginCommon .referralCol {
  overflow-y: auto;
  overflow-x: clip;
  height: 100vh;
  width: 603px;
}

@media (width >= 1400px) {
  .loginCommon .referralCol {
    width: 45%;
  }
}

.loginCommon .referralCol img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.loginCommon .loginCol {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

@media (width >= 992px) {
  .loginCommon .loginCol {
    width: calc(100% - 603px);
  }
}

@media (width >= 1400px) {
  .loginCommon .loginCol {
    width: 55%;
  }
}

@media (width <= 991px) {
  .loginCommon .loginCol {
    min-height: 100vh;
  }
}

.loginCommon_rightSide {
  padding: 1rem;
  width: 100%;
}

@media (width <= 991px) {
  .loginCommon_rightSide {
    padding: 3rem 1rem 1.5rem;
  }
}

.loginCommon_rightSide_inner {
  max-width: 553px;
  margin: 0 auto;
}

.loginCommon_rightSide_formBox {
  width: 100%;
  background-color: #9f9f9f1a;
  border-radius: 50px;
  padding: 2.5rem 4.35rem;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(4px);
}

@media (width <= 991px) {
  .loginCommon_rightSide_formBox {
    padding: 2rem 1rem;
  }
}

.loginCommon_rightSide_formBox:after {
  content: "";
  position: absolute;
  top: 0;
  left: -90px;
  width: 100%;
  height: 100%;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-for-stock-traders.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  z-index: -1;
}

.loginCommon_rightSide .thirdParty_login {
  margin-top: 1.25rem;
}

.loginCommon_rightSide .thirdParty_login_btn {
  background-color: #ffffffe6;
  width: 30px;
  height: 30px;
  border-radius: .625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 1rem;
  border: 0;
  transition: all .3s ease-in-out;
}

.loginCommon_rightSide .thirdParty_login_btn:last-child {
  margin-right: 0;
}

.loginCommon_rightSide .thirdParty_login_btn:hover {
  background-color: #fff;
}

.loginCommon_rightSide .orLine {
  margin: 1.25rem 0;
  text-align: center;
  position: relative;
}

.loginCommon_rightSide .orLine span {
  color: #fff;
  display: inline-block;
  margin: 0 auto;
}

.loginCommon_rightSide .orLine:after, .loginCommon_rightSide .orLine:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 30%;
  height: 1px;
  background-color: #666;
}

@media (width <= 575px) {
  .loginCommon_rightSide .orLine:after, .loginCommon_rightSide .orLine:before {
    width: 25%;
  }
}

.loginCommon_rightSide .orLine:before {
  left: auto;
  right: 0;
}

.loginCommon .forgot_form .orLine:after, .loginCommon .forgot_form .orLine:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 20%;
  height: 1px;
  background-color: #666;
}

@media (width <= 575px) {
  .loginCommon .forgot_form .orLine:after, .loginCommon .forgot_form .orLine:before {
    width: 15%;
  }
}

.loginCommon .forgot_form .orLine:before {
  left: auto;
  right: 0;
}

.loginCommon .loginHeading h1 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 6px;
  text-align: center;
}

@media (width <= 991px) {
  .loginCommon .loginHeading h1 {
    font-size: 1rem;
  }
}

.loginCommon .Forgotpassoword {
  margin-top: -1rem;
}

.loginCommon .Forgotpassoword a {
  font-weight: 700;
  color: #35c7ff;
}

@media (width <= 1199px) {
  .loginCommon .Forgotpassoword a {
    font-size: 1rem;
  }
}

.loginCommon .Forgotpassoword a:hover {
  color: #00adef;
  opacity: .8;
}

.loginCommon .anAccount h6 {
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  text-align: center;
  color: #00adef;
  max-width: 266px;
  margin: 0 auto;
}

.loginCommon .signup_form .anAccount h6 {
  color: #c5c5c5;
}

.loginCommon .login_footer_links {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.loginCommon .login_footer_links a {
  color: #c5c5d5;
  font-size: 15px;
  line-height: 12px;
  font-weight: 400;
  text-transform: uppercase;
  border-right: 1px solid #c5c5d5;
  padding: 0 .625rem;
}

@media (width <= 991px) {
  .loginCommon .login_footer_links a {
    font-size: 14px;
    padding: 0 .625rem;
    margin-top: .5rem;
  }
}

.loginCommon .login_footer_links a:last-child {
  border-right: 0;
}

.loginCommon .login_footer_links a:hover {
  color: #00adef;
}

.loginCommon .login_footer_links p {
  font-size: 18px;
  font-weight: 400;
}

.loginCommon .backbtn {
  margin-bottom: 20px;
}

.loginCommon .backbtn a {
  display: flex;
  align-items: center;
  font-size: 1.125rem;
  font-weight: 600;
  color: #fff;
}

.loginCommon .backbtn a img, .loginCommon .backbtn a svg {
  transform: rotate(180deg);
  margin-right: 10px;
  transition: all .3s ease-in-out;
}

.loginCommon .backbtn a:hover {
  color: #00adef;
}

.loginCommon .backbtn a:hover img, .loginCommon .backbtn a:hover svg {
  margin-right: 12px;
}

.loginCommon .backbtn a:hover img path, .loginCommon .backbtn a:hover svg path {
  fill: #00adef;
}

.authEmailBg {
  background-color: #011132;
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.authEmailBg_authContainer {
  width: 650px;
  padding: 20px;
}

.authEmailBg_authContainer img {
  height: 80px;
  margin-bottom: 15px;
}

.authEmailBg_authContainer_inner {
  background-color: #1f2a3e;
  width: 100%;
  padding: 25px 15px;
  border-radius: 10px;
  border: 1px solid #ffffff50;
}

.authEmailBg_authContainer_inner p {
  font-size: 14px;
  margin-bottom: 15px;
}

.authEmailBg_authContainer_inner p a {
  margin-bottom: 0 !important;
}

.authEmailBg_authContainer_inner a {
  margin-bottom: 15px;
}

.authEmailBg_authContainer_inner a button {
  background-color: #00adef;
  padding: 10px 25px;
  color: #fff;
  border-radius: 6px;
  font-size: 24px;
  font-weight: 600;
}

.authEmailBg_authContainer_inner_user p:first-child {
  margin-bottom: 0 !important;
}

.authEmailBg_authContainer_inner_user span {
  color: #2ec735;
}

.authEmailBg_authContainer_outer {
  margin-top: 20px;
}

.authEmailBg_authContainer_outer p {
  font-size: 14px;
  text-align: center;
  margin-bottom: 5px;
}

.authCorrectIcon {
  position: relative;
}

.authCorrectIcon .checkIcon {
  position: absolute;
  left: -40px;
  top: 18px;
}

.authCorrectIcon svg {
  height: 22px;
}

.invalid_credential {
  display: flex;
  gap: 10px;
  align-items: center;
  background-color: #1e222d;
  width: fit-content;
  padding: 10px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  border: 1px solid #ff696a !important;
}

.signup_invalid_credential {
  background-color: #1e222d;
  width: fit-content;
  padding: 10px;
  border-radius: 10px;
  border: 1px solid #ff696a !important;
}

.signup_invalid_credential p, .signup_invalid_credential span {
  font-size: 14px;
}

.signup_invalid_credential p a, .signup_invalid_credential span a {
  font-weight: 700;
  font-size: 16px !important;
}

.session-expire-message {
  background-color: #ffa9a9;
  color: #000;
  border-radius: 2rem;
  padding: 10px 30px;
  text-align: center;
  font-weight: 600;
  width: fit-content;
  margin-bottom: 10px;
}

/*# sourceMappingURL=src_css_auth_authGlobals_scss_b52d8e88.css.map*/