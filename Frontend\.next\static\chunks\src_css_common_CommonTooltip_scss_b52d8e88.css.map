{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/common/CommonTooltip.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.tooltip-container{position:relative;display:inline-block;cursor:pointer}.tooltip-container img{max-width:20px;min-width:20px;min-height:20px;max-height:20px}.tooltip-wrapper{flex-shrink:0;cursor:pointer}.tooltip-box{position:absolute;padding:5px 10px;z-index:1000;min-width:300px;width:300px;background-color:rgba(2.55,45.9,89.25,.699);color:#fff;text-align:left;padding:10px 15px;border-radius:5px;backdrop-filter:blur(6px);pointer-events:auto !important}.tooltip-box p,.tooltip-box a{font-size:.875rem !important;font-weight:300;line-height:20px}@media screen and (max-width: 991px){.tooltip-box p,.tooltip-box a{font-size:14px;line-height:18px}.tooltip-box{min-width:200px;width:200px}}.tooltip-top-left{top:0;right:0;transform:translateY(-100%)}.tooltip-top-right{top:0;left:0;transform:translateY(-100%)}.tooltip-bottom-left{bottom:0;right:0;transform:translateY(100%)}.tooltip-bottom-right{bottom:0;left:0;transform:translateY(100%)}.tooltip-center-bottom{top:25px;left:50%;transform:translateX(-50%)}.tooltip-center-top{bottom:100%;left:50%;transform:translateX(-50%) translateY(-10px)}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;AAAyE;;;;;;;AAAqF;;;;;AAA8C;;;;;;;;;;;;;;;AAA6P;;;;;;AAA4F;EAAqC;;;;;EAA8D;;;;;;AAA0C;;;;;;AAA4D;;;;;;AAA4D;;;;;;AAAiE;;;;;;AAAiE;;;;;;AAAoE"}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}