/* [project]/src/css/Home/LearningResources.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.learning_resources {
  padding: 84px 0;
  background: radial-gradient(50% 50%, #00b9ff66 21.5%, #00539966 100%), linear-gradient(135deg, #fff0 0%, #ffffff26 47.5%, #fff0 100%);
  position: relative;
  z-index: 1;
}

@media (width <= 767px) {
  .learning_resources {
    padding: 60px 0;
  }
}

.learning_resources_heading {
  max-width: 780px;
  margin: 0 auto;
}

.learning_resources_heading h2 {
  text-align: left;
}

@media (width <= 767px) {
  .learning_resources_heading h2 {
    text-align: center;
  }
}

.learning_resources_heading p {
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  letter-spacing: -1px;
  text-align: left;
  padding-top: 2rem;
}

@media (width <= 767px) {
  .learning_resources_heading p {
    text-align: center;
    padding-top: 1rem;
    font-size: 18px;
    line-height: 24px;
  }
}

.learning_resources_content {
  max-width: 964px;
  margin: 0 auto;
  padding-top: 50px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

@media (width <= 767px) {
  .learning_resources_content {
    padding-top: 30px;
  }
}

.learning_resources_content_col {
  width: 50%;
  padding: 0 35px;
}

@media (width <= 991px) {
  .learning_resources_content_col {
    padding: 0 25px;
  }
}

@media (width <= 767px) {
  .learning_resources_content_col {
    width: 100%;
    padding: 20px 0 0;
  }
}

.learning_resources_content_card {
  height: 350px;
  border: 4px solid #00adef;
  border-radius: 70px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: block;
}

@media (width <= 767px) {
  .learning_resources_content_card {
    height: 150px;
    border-radius: 2rem;
  }
}

.learning_resources_content_card:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0%;
  width: 100%;
  height: 100%;
  background: #0009;
  border-radius: 70px;
  z-index: 0;
}

@media (width <= 767px) {
  .learning_resources_content_card:after {
    border-radius: 2rem;
  }
}

.learning_resources_content_card img {
  border-radius: 70px;
  height: 100%;
  width: 100%;
  object-fit: cover;
  transition: all .3s ease-in-out;
}

@media (width <= 767px) {
  .learning_resources_content_card img {
    border-radius: 2rem;
  }
}

.learning_resources_content_card:hover img {
  transform: scale(1.1);
}

.learning_resources_content_card h3 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-weight: 600;
  z-index: 1;
  text-align: center;
  width: 100%;
  font-size: 3rem;
}

@media (width <= 991px) {
  .learning_resources_content_card h3 {
    font-size: 35px;
  }
}

@media (width <= 767px) {
  .learning_resources_content_card h3 {
    font-size: 18px;
  }
}

/*# sourceMappingURL=src_css_Home_LearningResources_scss_b52d8e88.css.map*/