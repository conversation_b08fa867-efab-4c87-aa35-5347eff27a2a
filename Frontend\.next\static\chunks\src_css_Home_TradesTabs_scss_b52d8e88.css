/* [project]/src/css/Home/TradesTabs.scss.css [app-client] (css) */
:root {
  --font-g<PERSON>roy: "<PERSON><PERSON>", sans-serif;
}

.trades_tabs {
  position: relative;
  z-index: 1;
  background-color: #042053;
}

.trades_tabs .tab-content {
  margin-top: 5rem;
}

@media screen and (width <= 991px) {
  .trades_tabs .tab-content {
    margin-top: 3rem;
  }
}

.trades_tabs .tab-content h3 {
  padding: 0 1rem;
  font-size: 2.8rem;
  font-weight: 800;
}

.trades_tabs_content figure img {
  display: block;
  margin: 0 auto;
}

@media screen and (width <= 991px) {
  .trades_tabs_content figure img {
    height: 500px;
    object-fit: cover;
  }
}

@media screen and (width <= 767px) {
  .trades_tabs_content figure img {
    height: 300px;
    object-fit: cover;
  }
}

@media (width <= 767px) {
  .icons_big_tabs .position-relative {
    max-width: 260px;
    margin: 0 auto;
  }

  .icons_big_tabs .big_tabs {
    display: flex;
    flex-wrap: nowrap;
    white-space: nowrap;
    overflow-x: hidden;
  }
}

.icons_big_tabs .big_tabs.nav {
  margin: 0 -10px;
}

@media (width <= 767px) {
  .icons_big_tabs .big_tabs.nav {
    margin: 0 10px;
  }
}

.icons_big_tabs .big_tabs.nav .nav-item {
  display: flex;
  width: 16.66%;
  padding: 0 10px;
}

@media (width <= 1199px) {
  .icons_big_tabs .big_tabs.nav .nav-item {
    width: 33.33%;
    padding: 10px;
  }
}

@media (width <= 767px) {
  .icons_big_tabs .big_tabs.nav .nav-item {
    width: 100%;
    min-width: 240px;
  }
}

.icons_big_tabs .tab-content {
  margin-top: 5rem;
}

@media screen and (width <= 991px) {
  .icons_big_tabs .tab-content {
    margin-top: 3rem;
  }
}

.icons_big_tabs .scroll-btn {
  background-color: #00adef;
  color: #fff;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 1.2rem;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rem;
  position: absolute;
  display: none;
  top: 50%;
  transform: translateY(-50%);
}

@media (width <= 767px) {
  .icons_big_tabs .scroll-btn {
    display: flex;
  }
}

.icons_big_tabs .scroll-btn.left {
  left: -30px;
}

.icons_big_tabs .scroll-btn.left svg, .icons_big_tabs .scroll-btn.left img {
  transform: rotate(180deg);
}

.icons_big_tabs .scroll-btn.right {
  right: -30px;
}

.icons_big_tabs .scroll-btn:hover {
  background-color: #00adef;
}

.icons_big_tabs .scroll-btn.disabled, .icons_big_tabs .scroll-btn:disabled {
  background-color: #414c60;
}

/*# sourceMappingURL=src_css_Home_TradesTabs_scss_b52d8e88.css.map*/