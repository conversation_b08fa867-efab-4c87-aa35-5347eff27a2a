{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/dashboard/Sidebar.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.admin_sidebar_wrapper{position:relative}.admin_sidebar_wrapper .linkList{border-bottom:1px solid #00adef}@media(max-width: 991px){.admin_sidebar_wrapper .linkList{border:none}}.admin_sidebar_wrapper .linkList:last-child{margin-bottom:0;border-bottom:0}.admin_sidebar_wrapper .linkList a{display:flex;align-items:center;padding-bottom:3px;padding-top:3px}.admin_sidebar_wrapper .linkList a .linktext{display:flex;align-items:center;justify-content:center;color:#00adef;text-decoration:none;padding:.5rem 0;font-size:1.25rem;font-weight:600;line-height:1.4;width:60px;height:60px;border-radius:10rem;border:2px solid #00adef;background-color:#04498c;transition:all ease-in-out .3s}.admin_sidebar_wrapper .linkList a .linktext svg{width:30px}@media(max-width: 991px){.admin_sidebar_wrapper .linkList a .linktext{width:56px;height:56px}}@media screen and (min-width: 992px){.admin_sidebar_wrapper .linkList a:hover,.admin_sidebar_wrapper .linkList a.active{background-color:#283f67}}.admin_sidebar_wrapper .linkList a:hover .linktext,.admin_sidebar_wrapper .linkList a.active .linktext{border-color:#32cd33;box-shadow:0 0 10px 4px rgba(50,205,51,.5)}.admin_sidebar_wrapper .linkList a .fulltext{color:#fff;font-size:1.15rem;padding-left:.625rem;font-weight:600}@media(max-width: 1279px){.admin_sidebar_wrapper .linkList a .fulltext{font-size:1rem}}@media(max-width: 991px){.admin_sidebar_wrapper .linkList a .fulltext{display:none}}.admin_sidebar .scroll-btn{background-color:#00adef;color:#fff;border:none;padding:0;cursor:pointer;font-size:1.2rem;min-width:30px;min-height:30px;display:flex;align-items:center;justify-content:center;border-radius:10rem;position:absolute;display:none;top:50%;transform:translateY(-50%);z-index:2}@media(max-width: 767px){.admin_sidebar .scroll-btn{display:flex}}.admin_sidebar .scroll-btn.left{left:10px}.admin_sidebar .scroll-btn.left img{transform:rotate(180deg)}.admin_sidebar .scroll-btn.right{right:10px}.admin_sidebar .scroll-btn:hover{background-color:#00adef}.admin_sidebar .scroll-btn.disabled,.admin_sidebar .scroll-btn:disabled{background-color:#414c60}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;AAAyC;;;;AAAiE;EAAyB;;;;;AAA8C;;;;;AAA4E;;;;;;;AAAsG;;;;;;;;;;;;;;;;;;AAAqU;;;;AAA4D;EAAyB;;;;;;AAAqE;EAAqC;;;;;AAA6G;;;;;AAAuK;;;;;;;AAA+G;EAA0B;;;;;AAA6D;EAAyB;;;;;AAA2D;;;;;;;;;;;;;;;;;;;;AAA2S;EAAyB;;;;;AAAyC;;;;AAA0C;;;;AAA6D;;;;AAA4C;;;;AAA0D"}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}