/* [project]/src/css/Home/BannerSec.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.banner_sec {
  position: relative;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-trade-analysis.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  padding: 1.35rem 0;
  min-height: 566px;
  z-index: 2;
  display: flex;
  align-items: center;
}

@media (width >= 1366px) {
  .banner_sec {
    min-height: calc(100vh - 90px);
  }
}

@media (width <= 1199px) {
  .banner_sec {
    background-position: 100%;
  }
}

@media (width <= 767px) {
  .banner_sec {
    flex-direction: column;
    background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-visual-kpis.png");
    padding-top: 47px;
    padding-bottom: 71px;
    background-size: contain;
    min-height: auto;
  }
}

.banner_sec:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(#000607 0%, #031940 83.43%);
  opacity: .5;
  z-index: -1;
}

.banner_sec_content {
  position: relative;
  z-index: 1;
  margin-top: -10%;
}

@media (width <= 767px) {
  .banner_sec_content {
    margin-top: 0;
  }
}

.banner_sec_content h1 {
  font-size: 100px;
  line-height: 110px;
  max-width: 990px;
  font-weight: bold;
  position: relative;
  z-index: 1;
}

@media (width <= 1399px) {
  .banner_sec_content h1 {
    font-size: 90px;
    line-height: 90px;
    max-width: 902px;
  }
}

@media (width <= 1199px) {
  .banner_sec_content h1 {
    font-size: 70px;
    line-height: 80px;
    max-width: 702px;
  }
}

@media (width <= 991px) {
  .banner_sec_content h1 {
    font-size: 55px;
    line-height: 64px;
    max-width: 602px;
  }
}

@media (width <= 767px) {
  .banner_sec_content h1 {
    font-size: 48px;
    line-height: 52px;
    max-width: 600px;
    text-align: center;
  }
}

@media (width <= 374px) {
  .banner_sec_content h1 {
    font-size: 40px;
    line-height: 48px;
    max-width: 500px;
  }
}

.banner_sec_search {
  text-align: center;
  max-width: 388px;
  width: 100%;
  margin: 0 auto 0;
  position: absolute;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

@media (width <= 767px) {
  .banner_sec_search {
    width: 90%;
    position: static;
    transform: none;
    margin-top: 30px;
  }
}

.banner_sec_search .form-label {
  font-size: 28px;
  font-weight: bold;
  line-height: 40.7px;
  letter-spacing: -1px;
  color: #fff;
}

@media (width <= 1199px) {
  .banner_sec_search .form-label {
    font-size: 24px;
    line-height: 34px;
  }
}

@media (width <= 991px) {
  .banner_sec_search .form-label {
    font-size: 18px;
    line-height: 30px;
  }
}

.banner_sec_overlayimg {
  position: absolute;
  bottom: 0;
  right: 0;
}

@media (width <= 991px) {
  .banner_sec_overlayimg {
    bottom: 90px;
  }
}

@media (width <= 767px) {
  .banner_sec_overlayimg {
    position: static;
  }
}

.banner_sec_overlayimg img {
  width: 526px;
}

@media (width >= 1680px) {
  .banner_sec_overlayimg img {
    width: 650px;
  }
}

@media (width <= 991px) {
  .banner_sec_overlayimg img {
    width: 347px;
  }
}

/*# sourceMappingURL=src_css_Home_BannerSec_scss_b52d8e88.css.map*/